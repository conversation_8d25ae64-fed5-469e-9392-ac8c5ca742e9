<<<<<<< HEAD
body {
    font-family: 'Poppins', sans-serif;
    background-color: #f0f4f8;
    color: #333;
    margin: 0;
    padding: 0;
    min-height: 100vh;
    transition: background-color 0.5s ease;
}

.page-container {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    min-height: calc(100vh - 60px);
    padding: 20px;
    margin-top: 60px;
    transition: margin 0.3s ease;
}

.container {
    background-color: #fff;
    padding: 30px 40px;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 800px;
    text-align: center;
    margin: 0 auto;
}

.main-nav {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 60px;
    background-color: #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    z-index: 1000;
    transition: all 0.3s ease;
    box-sizing: border-box; /* Ensure padding is included in width calculation */
}

.main-nav.sidebar {
    width: 240px;
    height: 100vh;
    flex-direction: column;
    align-items: flex-start;
    padding: 20px;
    overflow-y: auto;
}

.main-nav.sidebar.right {
    left: auto;
    right: 0;
}

.main-nav.sidebar .logo {
    margin-bottom: 30px;
}

.main-nav.sidebar .nav-links {
    flex-direction: column;
    width: 100%;
}

.main-nav.sidebar .nav-links li {
    margin: 5px 0;
    width: 100%;
}

.main-nav.sidebar .nav-item {
    display: block;
    padding: 10px 15px;
    width: 100%;
    box-sizing: border-box;
}

.main-nav.sidebar .profile-btn {
    margin-top: 30px;
}

.nav-toggle-btn {
    position: fixed;
    top: 10px;
    left: 10px;
    z-index: 1001;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #3498db;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    display: none;
}

.nav-toggle-btn .toggle-icon {
    width: 20px;
    height: 2px;
    background-color: white;
    position: relative;
    transition: all 0.3s ease;
}

.nav-toggle-btn .toggle-icon::before,
.nav-toggle-btn .toggle-icon::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 2px;
    background-color: white;
    transition: all 0.3s ease;
}

.nav-toggle-btn .toggle-icon::before {
    transform: translateY(-6px);
}

.nav-toggle-btn .toggle-icon::after {
    transform: translateY(6px);
}

.nav-toggle-btn.active .toggle-icon {
    background-color: transparent;
}

.nav-toggle-btn.active .toggle-icon::before {
    transform: rotate(45deg);
}

.nav-toggle-btn.active .toggle-icon::after {
    transform: rotate(-45deg);
}

.nav-position-toggle {
    margin: 0 15px;
    flex-shrink: 0; /* Prevent shrinking */
}

.nav-position-toggle button {
    background-color: #f0f4f8;
    border: none;
    padding: 5px 10px;
    border-radius: 15px;
    cursor: pointer;
    font-size: 0.8em;
    transition: all 0.3s ease;
}

.nav-position-toggle button:hover {
    background-color: #3498db;
    color: white;
}

.main-nav.sidebar .nav-position-toggle {
    margin: 15px 0;
}

.logo {
    display: flex;
    align-items: center;
    flex-shrink: 0; /* Prevent logo from shrinking */
    margin-right: 10px; /* Add right margin */
}

.logo-icon {
    height: 36px;
    margin-right: 10px;
}

.logo span {
    font-weight: 600;
    font-size: 1.2rem;
    color: #2c3e50;
}

.nav-links {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    flex-grow: 1; /* Allow nav links to take available space */
    justify-content: center; /* Center the nav links */
    min-width: 0; /* Allow container to shrink below content size */
    overflow: hidden; /* Prevent overflow */
}

.nav-links li {
    margin: 0 10px;
    white-space: nowrap; /* Prevent text wrapping */
    overflow: hidden; /* Hide overflow */
    text-overflow: ellipsis; /* Show ellipsis for overflow */
    flex-shrink: 1; /* Allow shrinking */
}

.nav-item {
    text-decoration: none;
    color: #7f8c8d;
    font-weight: 500;
    padding: 8px 15px;
    border-radius: 20px;
    transition: all 0.3s ease;
}

.nav-item:hover {
    color: #3498db;
    background-color: #f8f9fa;
}

.nav-item.active {
    color: #3498db;
    background-color: #ecf0f1;
}

.profile-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    overflow: hidden;
    cursor: pointer;
    border: 2px solid #ecf0f1;
    transition: all 0.3s ease;
    margin-left: 10px; /* Add left margin for spacing */
    flex-shrink: 0; /* Prevent shrinking */
    position: relative; /* For positioning */
}

.profile-btn:hover {
    border-color: #3498db;
}

.profile-btn img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.page {
    width: 100%;
}

.page.hidden {
    display: none;
}

.activities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.activity-card {
    background-color: #f9f9f9;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.activity-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.activity-card h3 {
    margin-top: 0;
    color: #2c3e50;
    font-size: 1.3em;
}

.hidden {
    display: none;
}

.activity-btn {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 8px 16px;
    font-size: 0.9em;
    border-radius: 20px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    margin-top: 10px;
}

.activity-btn:hover {
    background-color: #2980b9;
}

.hidden {
    display: none;
}

.play-btn {
    background-color: #2ecc71;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 20px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.play-btn:hover {
    background-color: #27ae60;
}

.play-btn.playing {
    background-color: #e74c3c;
}

/* AI Music Generator */
.ai-music-generator {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.ai-music-options {
    display: flex;
    flex-direction: column;
    gap: 12px;
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 8px;
}

.ai-option-group {
    display: flex;
    align-items: center;
}

.ai-option-group label {
    min-width: 70px;
    font-weight: 500;
}

.ai-select {
    flex-grow: 1;
    padding: 8px;
    border-radius: 5px;
    border: 1px solid #ddd;
    background-color: white;
}

.ai-music-result {
    margin-top: 10px;
    min-height: 100px;
}

.ai-generating {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
    text-align: center;
}

.ai-generating-animation {
    display: flex;
    gap: 6px;
}

.ai-generating-animation span {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #3498db;
    animation: generating-bounce 1.4s infinite ease-in-out;
}

.ai-generating-animation span:nth-child(1) {
    animation-delay: 0s;
}

.ai-generating-animation span:nth-child(2) {
    animation-delay: 0.2s;
}

.ai-generating-animation span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes generating-bounce {
    0%, 60%, 100% { transform: translateY(0); }
    30% { transform: translateY(-6px); }
}

.ai-generating p {
    color: #7f8c8d;
    margin: 0;
}

.ai-track-player {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 15px;
}

.ai-track-info {
    margin-bottom: 15px;
}

.ai-track-info h4 {
    margin: 0 0 5px 0;
    color: #2c3e50;
}

.ai-track-info p {
    margin: 0;
    font-size: 0.9em;
    color: #7f8c8d;
}

.ai-player-controls {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.ai-player-progress {
    display: flex;
    align-items: center;
    gap: 10px;
}

.progress-bar {
    flex-grow: 1;
    height: 6px;
    background-color: #ecf0f1;
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background-color: #3498db;
    width: 0%;
    transition: width 0.3s ease;
}

.ai-volume-control {
    display: flex;
    align-items: center;
    gap: 10px;
}

.breathing-animation {
    width: 100px;
    height: 100px;
    background-color: #3498db;
    border-radius: 50%;
    margin: 20px auto;
    transition: transform 4s ease, background-color 4s ease;
}

.breathing-animation.inhale {
    transform: scale(1.5);
    background-color: #2ecc71;
}

.breathing-animation.exhale {
    transform: scale(1);
    background-color: #3498db;
}

.stretch-timer {
    font-size: 2em;
    margin: 10px 0;
    font-weight: 600;
    color: #2c3e50;
}

.stretch-controls {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 15px;
}

.stretch-btn {
    background-color: #ecf0f1;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    font-size: 1.2em;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.stretch-btn:hover {
    background-color: #d5dbdb;
}

.mindfulness-timer {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.timer-display {
    font-size: 2.5em;
    font-weight: 600;
    color: #2c3e50;
    margin: 10px 0;
}

.timer-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.time-btn {
    background-color: #ecf0f1;
    border: none;
    padding: 6px 12px;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.time-btn:hover {
    background-color: #d5dbdb;
}

.time-btn.active {
    background-color: #3498db;
    color: white;
}

/* Model selector styles */
.model-selector {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 15px;
    padding: 10px;
    background: rgba(52, 152, 219, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(52, 152, 219, 0.2);
}

.model-selector label {
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.model-select {
    padding: 5px 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background: white;
    font-size: 0.9em;
    cursor: pointer;
}

.model-status {
    font-size: 0.8em;
    padding: 3px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.model-status.online {
    background: #2ecc71;
    color: white;
}

.model-status.offline {
    background: #3498db;
    color: white;
}

.model-status.checking {
    background: #f39c12;
    color: white;
}

.model-status.error {
    background: #e74c3c;
    color: white;
}

.chat-container {
    background-color: #0d1117;
    border-radius: 16px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 650px;
    margin-top: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25),
                0 0 40px rgba(52, 152, 219, 0.1),
                inset 0 0 20px rgba(52, 152, 219, 0.05);
    border: 1px solid rgba(52, 152, 219, 0.3);
    position: relative;
}

.chat-messages {
    flex-grow: 1;
    overflow-y: auto;
    padding: 25px;
    display: flex;
    flex-direction: column;
    gap: 18px;
    scroll-behavior: smooth;
    background: linear-gradient(to bottom,
                rgba(13, 17, 23, 0.99),
                rgba(13, 17, 23, 0.95)),
                url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0zNiAxOGMwLTkuOTQtOC4wNi0xOC0xOC0xOFYyYzguODQgMCAxNiA3LjE2IDE2IDE2IDAgOC44NC03LjE2IDE2LTE2IDE2djJjOS45NCAwIDE4LTguMDYgMTgtMTgiIGZpbGw9IiMxYTFhMWEiLz48L2c+PC9zdmc+');
    position: relative;
}

.chat-messages:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: linear-gradient(to bottom, rgba(13, 17, 23, 1) 0%, rgba(13, 17, 23, 0) 100%);
    pointer-events: none;
    z-index: 1;
}

.chat-messages:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: linear-gradient(to top, rgba(13, 17, 23, 1) 0%, rgba(13, 17, 23, 0) 100%);
    pointer-events: none;
    z-index: 1;
}

.message {
    display: flex;
    position: relative;
    z-index: 2;
}

.message-avatar {
    width: 38px;
    height: 38px;
    border-radius: 50%;
    margin-right: 12px;
    background-size: cover;
    background-position: center;
    flex-shrink: 0;
    position: relative;
}

.ai-message .message-avatar {
    background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxjaXJjbGUgY3g9IjEwMCIgY3k9IjEwMCIgcj0iMTAwIiBmaWxsPSIjMDU0QThCIi8+CjxwYXRoIGQ9Ik02NSA2NUgxMzVWMTM1SDY1VjY1WiIgZmlsbD0iIzA1NEE4QiIgc3Ryb2tlPSIjOEVDNUZDIiBzdHJva2Utd2lkdGg9IjQiLz4KPHBhdGggZD0iTTY1IDY1TDEzNSAxMzUiIHN0cm9rZT0iIzhFQzVGQyIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxwYXRoIGQ9Ik0xMzUgNjVMNjUgMTM1IiBzdHJva2U9IiM4RUM1RkMiIHN0cm9rZS13aWR0aD0iNCIvPgo8Y2lyY2xlIGN4PSIxMDAiIGN5PSIxMDAiIHI9IjE1IiBmaWxsPSIjOEVDNUZDIi8+Cjwvc3ZnPgo=');
    border: 2px solid rgba(52, 152, 219, 0.5);
    box-shadow: 0 0 10px rgba(52, 152, 219, 0.3);
}

.user-message .message-avatar {
    background-image: url('https://via.placeholder.com/40');
    border: 2px solid rgba(46, 204, 113, 0.5);
    box-shadow: 0 0 10px rgba(46, 204, 113, 0.3);
    order: 1;
    margin-right: 0;
    margin-left: 12px;
}

.message-avatar::after {
    content: '';
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #2ecc71;
    border: 2px solid #0d1117;
}

.ai-message .message-avatar::after {
    background: #3498db;
}

.ai-message {
    max-width: 80%;
}

.user-message {
    align-self: flex-end;
    flex-direction: row-reverse;
}

.message-bubble {
    padding: 14px 18px;
    border-radius: 18px;
    line-height: 1.5;
    position: relative;
    transition: all 0.3s ease;
    max-width: calc(100% - 50px);
}

.ai-message .message-bubble {
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.15) 0%, rgba(52, 152, 219, 0.05) 100%);
    color: #e6f7ff;
    border: 1px solid rgba(52, 152, 219, 0.3);
    border-top-left-radius: 4px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1), 0 0 5px rgba(52, 152, 219, 0.1);
}

.user-message .message-bubble {
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.15) 0%, rgba(46, 204, 113, 0.05) 100%);
    color: #e6fff2;
    border: 1px solid rgba(46, 204, 113, 0.3);
    border-top-right-radius: 4px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1), 0 0 5px rgba(46, 204, 113, 0.1);
}

.message:hover .message-bubble {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15), 0 0 10px rgba(52, 152, 219, 0.2);
}

.message-time {
    font-size: 0.7em;
    color: rgba(255, 255, 255, 0.5);
    position: absolute;
    bottom: -20px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.ai-message .message-time {
    left: 0;
}

.user-message .message-time {
    right: 0;
}

.message:hover .message-time {
    opacity: 1;
}

.thinking {
    align-self: flex-start;
    width: fit-content;
    transition: opacity 0.3s ease;
}

.thinking .message-bubble {
    padding: 10px 16px;
}

/* Message content styles */
.message-content {
    transition: opacity 0.5s ease;
    font-size: 0.95rem;
    line-height: 1.5;
}

.message-content p {
    margin: 0 0 10px 0;
}

.message-content p:last-child {
    margin-bottom: 0;
}

/* Code block styling */
.message-content pre {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(52, 152, 219, 0.3);
    border-radius: 6px;
    padding: 12px;
    margin: 10px 0;
    overflow-x: auto;
    position: relative;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 0.85rem;
    color: #e6f7ff;
}

.message-content pre::before {
    content: 'code';
    position: absolute;
    top: -10px;
    left: 10px;
    background: #0d1117;
    padding: 0 8px;
    font-size: 0.7rem;
    color: rgba(52, 152, 219, 0.8);
    border-radius: 10px;
    border: 1px solid rgba(52, 152, 219, 0.3);
}

.message-content code {
    font-family: 'Consolas', 'Monaco', monospace;
    background: rgba(52, 152, 219, 0.1);
    padding: 2px 5px;
    border-radius: 3px;
    font-size: 0.85em;
    color: #8ec5fc;
}

/* Text effect styles */
.large-emoji {
    font-size: 1.5em;
    display: inline-block;
    margin: 0 2px;
    vertical-align: middle;
    transform-origin: center;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.highlight-text {
    background: linear-gradient(120deg, rgba(52, 152, 219, 0.2) 0%, rgba(52, 152, 219, 0.1) 100%);
    border-radius: 3px;
    padding: 0 3px;
    font-weight: 500;
    color: #8ec5fc;
}

.advice-heading {
    font-weight: 600;
    color: #2ecc71;
    display: block;
    margin-top: 10px;
    margin-bottom: 5px;
}

.list-item {
    display: block;
    margin: 5px 0;
    padding-left: 8px;
    border-left: 2px solid #3498db;
}

.typing-indicator {
    display: flex;
    gap: 5px;
    padding: 5px;
    align-items: center;
    height: 20px;
}

.typing-indicator span {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #3498db;
    opacity: 0.6;
    animation: typingBounce 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
    animation-delay: 0s;
}

.typing-indicator span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typingBounce {
    0%, 60%, 100% { transform: translateY(0); }
    30% { transform: translateY(-6px); }
}

/* Chat Controls */
.chat-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 18px;
    background-color: rgba(13, 17, 23, 0.9);
    border-top: 1px solid rgba(52, 152, 219, 0.2);
    position: relative;
    z-index: 5;
}

.chat-actions {
    display: flex;
    gap: 10px;
}

.chat-action-btn {
    background: rgba(52, 152, 219, 0.1);
    border: 1px solid rgba(52, 152, 219, 0.3);
    border-radius: 8px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.chat-action-btn:hover {
    background: rgba(52, 152, 219, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.action-icon {
    font-size: 1.2em;
}

.chat-status {
    font-size: 0.85em;
    color: rgba(255, 255, 255, 0.6);
}

.save-status {
    display: inline-block;
    transition: opacity 0.5s ease;
    opacity: 0;
}

.save-status.show {
    opacity: 1;
}

.chat-input {
    display: flex;
    padding: 18px;
    background-color: rgba(13, 17, 23, 0.8);
    border-top: 1px solid rgba(52, 152, 219, 0.2);
    position: relative;
    z-index: 5;
    backdrop-filter: blur(10px);
}

.chat-input::before {
    content: '';
    position: absolute;
    top: -1px;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(to right,
                transparent,
                rgba(52, 152, 219, 0.5),
                transparent);
}

.chat-input textarea {
    flex-grow: 1;
    padding: 14px 18px;
    background-color: rgba(255, 255, 255, 0.05);
    color: #e6f7ff;
    border: 1px solid rgba(52, 152, 219, 0.3);
    border-radius: 20px;
    resize: none;
    font-family: 'Poppins', sans-serif;
    height: 24px;
    max-height: 120px;
    transition: all 0.3s ease;
    box-shadow: 0 0 20px rgba(52, 152, 219, 0.05);
}

.chat-input textarea::placeholder {
    color: rgba(255, 255, 255, 0.3);
}

.chat-input textarea:focus {
    outline: none;
    border-color: rgba(52, 152, 219, 0.8);
    background-color: rgba(52, 152, 219, 0.1);
    height: 60px;
    box-shadow: 0 0 15px rgba(52, 152, 219, 0.2);
}

.chat-input button {
    margin-left: 12px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
    position: relative;
    overflow: hidden;
}

.chat-input button::before {
    content: '→';
    font-size: 1.5em;
    position: relative;
    z-index: 2;
}

.chat-input button::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
    z-index: 1;
}

.chat-input button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.chat-input button:active {
    transform: translateY(1px);
    box-shadow: 0 2px 10px rgba(52, 152, 219, 0.3);
}

.chat-suggestions {
    padding: 15px 18px;
    background-color: rgba(13, 17, 23, 0.7);
    border-top: 1px solid rgba(52, 152, 219, 0.1);
    backdrop-filter: blur(5px);
}

.chat-suggestions p {
    margin: 0 0 10px 0;
    font-size: 0.85em;
    color: rgba(255, 255, 255, 0.6);
    letter-spacing: 0.5px;
    text-transform: uppercase;
}

.suggestion-chips {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.suggestion-chip {
    background-color: rgba(52, 152, 219, 0.1);
    border: 1px solid rgba(52, 152, 219, 0.3);
    border-radius: 20px;
    padding: 8px 15px;
    font-size: 0.85em;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.suggestion-chip::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(transparent, rgba(52, 152, 219, 0.1), transparent);
    transform: rotate(30deg);
    transition: transform 0.5s ease;
}

.suggestion-chip:hover {
    background-color: rgba(52, 152, 219, 0.2);
    border-color: rgba(52, 152, 219, 0.5);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.2);
}

.suggestion-chip:hover::after {
    transform: translateX(100%) rotate(30deg);
}

.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1100;
    justify-content: center;
    align-items: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background-color: #fff;
    padding: 30px;
    border-radius: 12px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
}

/* Chat History Modal Styles */
.chat-history-modal {
    max-width: 900px;
    width: 90%;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
}

.chat-history-search {
    display: flex;
    margin-bottom: 15px;
    gap: 10px;
}

.chat-history-search input {
    flex-grow: 1;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 0.9em;
}

.chat-history-search button {
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 0 15px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.chat-history-search button:hover {
    background-color: #2980b9;
}

.chat-history-container {
    display: flex;
    gap: 20px;
    height: 400px;
    overflow: hidden;
}

.chat-history-list {
    width: 40%;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow-y: auto;
    background-color: #f9f9f9;
}

.chat-history-item {
    padding: 15px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.chat-history-item:hover {
    background-color: #f0f4f8;
}

.chat-history-item.selected {
    background-color: rgba(52, 152, 219, 0.1);
    border-left: 3px solid #3498db;
}

.chat-history-item-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
}

.chat-history-item-title {
    font-weight: 600;
    color: #2c3e50;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 70%;
}

.chat-history-item-date {
    font-size: 0.8em;
    color: #7f8c8d;
}

.chat-history-item-preview {
    font-size: 0.85em;
    color: #7f8c8d;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.chat-history-item-meta {
    display: flex;
    justify-content: space-between;
    font-size: 0.75em;
    color: #95a5a6;
    margin-top: 5px;
}

.chat-history-preview {
    width: 60%;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow-y: auto;
    background-color: #fff;
    padding: 15px;
}

.preview-placeholder {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #95a5a6;
    font-style: italic;
}

.preview-message {
    padding: 10px;
    margin-bottom: 10px;
    border-radius: 8px;
}

.preview-message.ai {
    background-color: #f0f4f8;
    border-left: 3px solid #3498db;
}

.preview-message.user {
    background-color: #f8f9fa;
    border-left: 3px solid #2ecc71;
}

.preview-message-header {
    font-weight: 600;
    margin-bottom: 5px;
    color: #2c3e50;
}

.preview-message-content {
    font-size: 0.9em;
    color: #34495e;
}

.chat-history-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
    gap: 10px;
}

.chat-history-actions button {
    padding: 8px 15px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.chat-history-actions button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

#loadSelectedChatBtn {
    background-color: #3498db;
    color: white;
}

#loadSelectedChatBtn:hover:not(:disabled) {
    background-color: #2980b9;
}

#deleteSelectedChatBtn, #deleteAllChatsBtn {
    background-color: #e74c3c;
    color: white;
}

#deleteSelectedChatBtn:hover:not(:disabled), #deleteAllChatsBtn:hover:not(:disabled) {
    background-color: #c0392b;
}

#exportSelectedChatBtn {
    background-color: #2ecc71;
    color: white;
}

#exportSelectedChatBtn:hover:not(:disabled) {
    background-color: #27ae60;
}

.empty-history-message {
    padding: 20px;
    text-align: center;
    color: #95a5a6;
    font-style: italic;
}

.preview-more-messages {
    text-align: center;
    padding: 10px;
    font-size: 0.85em;
    color: #95a5a6;
    font-style: italic;
    background-color: #f9f9f9;
    border-radius: 8px;
    margin-top: 10px;
}

.close-modal {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 1.5em;
    cursor: pointer;
    color: #7f8c8d;
}

.close-modal:hover {
    color: #2c3e50;
}

.profile-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-top: 15px;
}

.profile-avatar-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

#profileAvatarPreview {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #ecf0f1;
}

#changeAvatarBtn {
    background-color: #f0f4f8;
    border: none;
    padding: 8px 15px;
    border-radius: 20px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

#changeAvatarBtn:hover {
    background-color: #dde4eb;
}

.profile-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-right: 10px;
}

@media (max-width: 768px) {
    .main-nav {
        padding: 0 15px; /* Reduce padding on smaller screens */
    }

    .main-nav.sidebar {
        width: 100%;
        transform: translateX(-100%);
    }

    .main-nav.sidebar.right {
        transform: translateX(100%);
    }

    .main-nav.sidebar.active {
        transform: translateX(0);
    }

    .nav-toggle-btn {
        display: flex;
    }

    .page-container.nav-sidebar {
        margin-top: 0;
        margin-left: 0;
    }

    .page-container.nav-sidebar.right {
        margin-right: 0;
    }

    .container {
        padding: 20px;
    }

    .activities-grid {
        grid-template-columns: 1fr;
    }

    .profile-details {
        grid-template-columns: 1fr;
    }

    .nav-links li {
        margin: 0 3px; /* Reduce margins between nav items */
    }

    .nav-item {
        padding: 6px 8px; /* Reduce padding on nav items */
        font-size: 0.9em;
    }

    .logo span {
        font-size: 1rem; /* Smaller logo text */
    }

    .nav-position-toggle button {
        font-size: 0.7em; /* Smaller button text */
        padding: 4px 8px; /* Smaller button */
    }

    .profile-btn {
        width: 32px; /* Smaller profile button */
        height: 32px;
        margin-left: 5px; /* Less margin */
    }
}

/* Extra small devices */
@media (max-width: 480px) {
    .main-nav {
        padding: 0 10px; /* Even less padding */
    }

    .logo-icon {
        height: 30px; /* Smaller logo icon */
    }

    .nav-links {
        justify-content: flex-start; /* Left align nav links */
    }

    .nav-item {
        padding: 5px 6px; /* Smaller padding */
        font-size: 0.8em; /* Smaller font */
    }

    .nav-position-toggle {
        margin: 0 5px; /* Less margin */
    }
}

header {
    margin-bottom: 30px;
}

header h1 {
    color: #2c3e50;
    font-weight: 600;
    font-size: 2.2em;
    margin-bottom: 5px;
}

.ai-badge {
    background-color: #3498db;
    color: white;
    font-size: 0.5em;
    padding: 3px 8px;
    border-radius: 5px;
    vertical-align: middle;
    margin-left: 5px;
}

header p {
    color: #7f8c8d;
    font-size: 1.1em;
}

.input-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 30px;
}

.input-group {
    text-align: left;
}

.input-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 400;
    color: #34495e;
}

.input-group input[type="range"],
.input-group input[type="number"] {
    width: calc(100% - 60px); /* Adjust width if span is next to it */
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 6px;
    box-sizing: border-box;
    font-size: 1em;
}
.input-group input[type="number"] {
    width: 100%;
}

.input-group input[type="range"] {
    accent-color: #3498db;
    display: inline-block; /* For alignment with span */
    vertical-align: middle;
}

.input-group span {
    display: inline-block;
    margin-left: 10px;
    font-weight: 600;
    color: #3498db;
    vertical-align: middle;
    min-width: 50px; /* Ensure consistent spacing */
}

#predictButton {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 12px 25px;
    font-size: 1.1em;
    font-weight: 600;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    margin-top: 10px;
}

#predictButton:hover {
    background-color: #2980b9;
}

.result-area {
    background-color: #f9f9f9;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #eee;
    margin-top: 20px;
}

.result-area h2 {
    color: #2c3e50;
    margin-top: 0;
    font-size: 1.5em;
}

#predictionOutput p {
    font-size: 1.1em;
    line-height: 1.6;
    color: #555;
}

#predictionOutput p strong {
    color: #34495e;
}

#stressEmoji {
    font-size: 1.5em;
}

#loadingIndicator {
    text-align: center;
    padding: 20px;
}

.spinner {
    border: 6px solid #f3f3f3; /* Light grey */
    border-top: 6px solid #3498db; /* Blue */
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 10px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.stress-meter-container {
    margin: 20px 0;
    text-align: center;
}

.stress-meter {
    height: 20px;
    width: 100%;
    background-color: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 5px;
}

#stressMeterFill {
    height: 100%;
    background: linear-gradient(to right, #4CAF50, #FFEB3B, #FF9800, #F44336);
    width: 0;
    transition: width 1s ease-in-out;
}

.stress-labels {
    display: flex;
    justify-content: space-between;
    font-size: 0.8em;
    color: #666;
}

.personalized-tips {
    margin-top: 20px;
    text-align: left;
    background-color: rgba(255, 255, 255, 0.7);
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #3498db;
}

.personalized-tips h3 {
    margin-top: 0;
    color: #2c3e50;
    font-size: 1.2em;
}

.personalized-tips ul {
    margin: 10px 0 0 0;
    padding-left: 20px;
}

.personalized-tips li {
    margin-bottom: 8px;
    line-height: 1.4;
}

.history-section {
    margin-top: 30px;
    background-color: #f9f9f9;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #eee;
}

.chart-container {
    margin: 20px 0;
    height: 300px;
}

.history-list {
    margin-top: 20px;
    max-height: 200px;
    overflow-y: auto;
    padding: 10px;
    background-color: white;
    border-radius: 8px;
    border: 1px solid #eee;
}

.history-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.history-item:last-child {
    border-bottom: none;
}

.history-date {
    font-weight: 600;
    color: #2c3e50;
}

.history-level {
    padding: 3px 10px;
    border-radius: 20px;
    font-size: 0.9em;
}

.level-low {
    background-color: #e6ffed;
    color: #4CAF50;
}

.level-moderate {
    background-color: #fff9e6;
    color: #FF9800;
}

.level-high {
    background-color: #fff0e6;
    color: #FF9800;
}

.level-very-high {
    background-color: #ffe6e6;
    color: #F44336;
}

.save-button {
    background-color: #2ecc71;
    color: white;
    border: none;
    padding: 10px 20px;
    font-size: 1em;
    border-radius: 6px;
    cursor: pointer;
    margin-top: 15px;
    transition: background-color 0.3s ease;
}

.save-button:hover {
    background-color: #27ae60;
}

.clear-button {
    background-color: #e74c3c;
    color: white;
    border: none;
    padding: 8px 16px;
    font-size: 0.9em;
    border-radius: 6px;
    cursor: pointer;
    margin-top: 10px;
    transition: background-color 0.3s ease;
}

.clear-button:hover {
    background-color: #c0392b;
}

body.stress-low { background-color: #e6ffed; } /* Light Green */
body.stress-moderate { background-color: #fff9e6; } /* Light Yellow */
body.stress-high { background-color: #fff0e6; } /* Light Orange */
body.stress-very-high { background-color: #ffe6e6; } /* Light Red */

.slide-in-left {
    animation: slideInLeft 0.3s forwards;
}

.slide-in-right {
    animation: slideInRight 0.3s forwards;
}

.slide-out-left {
    animation: slideOutLeft 0.3s forwards;
}

.slide-out-right {
    animation: slideOutRight 0.3s forwards;
}

@keyframes slideInLeft {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

@keyframes slideOutLeft {
    from { transform: translateX(0); }
    to { transform: translateX(-100%); }
}

@keyframes slideOutRight {
    from { transform: translateX(0); }
    to { transform: translateX(100%); }
}

.welcome-message {
    text-align: center;
    padding: 18px;
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.1) 0%, rgba(52, 152, 219, 0.05) 100%);
    border-radius: 12px;
    margin-bottom: 20px;
    animation: fadeIn 0.8s;
    border: 1px solid rgba(52, 152, 219, 0.3);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1), 0 0 20px rgba(52, 152, 219, 0.1);
    position: relative;
    overflow: hidden;
}

.welcome-message::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg,
                transparent,
                rgba(52, 152, 219, 0.1),
                transparent);
    transform: translateX(-100%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.welcome-message p {
    margin: 0;
    font-weight: 500;
    color: #e6f7ff;
    font-size: 1.05rem;
    letter-spacing: 0.3px;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.avatar-options {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.avatar-selection {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    justify-content: center;
    margin-top: 15px;
}

.avatar-option {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    cursor: pointer;
    object-fit: cover;
    border: 3px solid transparent;
    transition: all 0.3s ease;
}

.avatar-option:hover, .avatar-option.selected {
    border-color: #3498db;
    transform: scale(1.1);
}

.avatar-selection-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1200;
    transition: opacity 0.3s ease;
}

.avatar-selection-modal.show {
    display: flex;
}

.avatar-modal-content {
    background-color: #fff;
    padding: 20px;
    border-radius: 12px;
    width: 90%;
    max-width: 440px;
    position: relative;
    max-height: 80vh;
    overflow-y: auto;
}

.avatar-modal-content h3 {
    margin-top: 0;
    margin-bottom: 15px;
    text-align: center;
}

.modal-close-btn {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 1.5em;
    cursor: pointer;
    color: #7f8c8d;
    transition: color 0.2s ease;
}

.modal-close-btn:hover {
    color: #e74c3c;
=======
body {
    font-family: 'Poppins', sans-serif;
    background-color: #f0f4f8;
    color: #333;
    margin: 0;
    padding: 0;
    min-height: 100vh;
    transition: background-color 0.5s ease;
}

.page-container {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    min-height: calc(100vh - 60px);
    padding: 20px;
    margin-top: 60px;
    transition: margin 0.3s ease;
}

.container {
    background-color: #fff;
    padding: 30px 40px;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 800px;
    text-align: center;
    margin: 0 auto;
}

.main-nav {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 60px;
    background-color: #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    z-index: 1000;
    transition: all 0.3s ease;
    box-sizing: border-box; /* Ensure padding is included in width calculation */
}

.main-nav.sidebar {
    width: 240px;
    height: 100vh;
    flex-direction: column;
    align-items: flex-start;
    padding: 20px;
    overflow-y: auto;
}

.main-nav.sidebar.right {
    left: auto;
    right: 0;
}

.main-nav.sidebar .logo {
    margin-bottom: 30px;
}

.main-nav.sidebar .nav-links {
    flex-direction: column;
    width: 100%;
}

.main-nav.sidebar .nav-links li {
    margin: 5px 0;
    width: 100%;
}

.main-nav.sidebar .nav-item {
    display: block;
    padding: 10px 15px;
    width: 100%;
    box-sizing: border-box;
}

.main-nav.sidebar .profile-btn {
    margin-top: 30px;
}

.nav-toggle-btn {
    position: fixed;
    top: 10px;
    left: 10px;
    z-index: 1001;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #3498db;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    display: none;
}

.nav-toggle-btn .toggle-icon {
    width: 20px;
    height: 2px;
    background-color: white;
    position: relative;
    transition: all 0.3s ease;
}

.nav-toggle-btn .toggle-icon::before,
.nav-toggle-btn .toggle-icon::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 2px;
    background-color: white;
    transition: all 0.3s ease;
}

.nav-toggle-btn .toggle-icon::before {
    transform: translateY(-6px);
}

.nav-toggle-btn .toggle-icon::after {
    transform: translateY(6px);
}

.nav-toggle-btn.active .toggle-icon {
    background-color: transparent;
}

.nav-toggle-btn.active .toggle-icon::before {
    transform: rotate(45deg);
}

.nav-toggle-btn.active .toggle-icon::after {
    transform: rotate(-45deg);
}

.nav-position-toggle {
    margin: 0 15px;
    flex-shrink: 0; /* Prevent shrinking */
}

.nav-position-toggle button {
    background-color: #f0f4f8;
    border: none;
    padding: 5px 10px;
    border-radius: 15px;
    cursor: pointer;
    font-size: 0.8em;
    transition: all 0.3s ease;
}

.nav-position-toggle button:hover {
    background-color: #3498db;
    color: white;
}

.main-nav.sidebar .nav-position-toggle {
    margin: 15px 0;
}

.logo {
    display: flex;
    align-items: center;
    flex-shrink: 0; /* Prevent logo from shrinking */
    margin-right: 10px; /* Add right margin */
}

.logo-icon {
    height: 36px;
    margin-right: 10px;
}

.logo span {
    font-weight: 600;
    font-size: 1.2rem;
    color: #2c3e50;
}

.nav-links {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    flex-grow: 1; /* Allow nav links to take available space */
    justify-content: center; /* Center the nav links */
    min-width: 0; /* Allow container to shrink below content size */
    overflow: hidden; /* Prevent overflow */
}

.nav-links li {
    margin: 0 10px;
    white-space: nowrap; /* Prevent text wrapping */
    overflow: hidden; /* Hide overflow */
    text-overflow: ellipsis; /* Show ellipsis for overflow */
    flex-shrink: 1; /* Allow shrinking */
}

.nav-item {
    text-decoration: none;
    color: #7f8c8d;
    font-weight: 500;
    padding: 8px 15px;
    border-radius: 20px;
    transition: all 0.3s ease;
}

.nav-item:hover {
    color: #3498db;
    background-color: #f8f9fa;
}

.nav-item.active {
    color: #3498db;
    background-color: #ecf0f1;
}

.profile-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    overflow: hidden;
    cursor: pointer;
    border: 2px solid #ecf0f1;
    transition: all 0.3s ease;
    margin-left: 10px; /* Add left margin for spacing */
    flex-shrink: 0; /* Prevent shrinking */
    position: relative; /* For positioning */
}

.profile-btn:hover {
    border-color: #3498db;
}

.profile-btn img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.page {
    width: 100%;
}

.page.hidden {
    display: none;
}

.activities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.activity-card {
    background-color: #f9f9f9;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.activity-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.activity-card h3 {
    margin-top: 0;
    color: #2c3e50;
    font-size: 1.3em;
}

.hidden {
    display: none;
}

.activity-btn {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 8px 16px;
    font-size: 0.9em;
    border-radius: 20px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    margin-top: 10px;
}

.activity-btn:hover {
    background-color: #2980b9;
}

.hidden {
    display: none;
}

.play-btn {
    background-color: #2ecc71;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 20px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.play-btn:hover {
    background-color: #27ae60;
}

.play-btn.playing {
    background-color: #e74c3c;
}

/* AI Music Generator */
.ai-music-generator {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.ai-music-options {
    display: flex;
    flex-direction: column;
    gap: 12px;
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 8px;
}

.ai-option-group {
    display: flex;
    align-items: center;
}

.ai-option-group label {
    min-width: 70px;
    font-weight: 500;
}

.ai-select {
    flex-grow: 1;
    padding: 8px;
    border-radius: 5px;
    border: 1px solid #ddd;
    background-color: white;
}

.ai-music-result {
    margin-top: 10px;
    min-height: 100px;
}

.ai-generating {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
    text-align: center;
}

.ai-generating-animation {
    display: flex;
    gap: 6px;
}

.ai-generating-animation span {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #3498db;
    animation: generating-bounce 1.4s infinite ease-in-out;
}

.ai-generating-animation span:nth-child(1) {
    animation-delay: 0s;
}

.ai-generating-animation span:nth-child(2) {
    animation-delay: 0.2s;
}

.ai-generating-animation span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes generating-bounce {
    0%, 60%, 100% { transform: translateY(0); }
    30% { transform: translateY(-6px); }
}

.ai-generating p {
    color: #7f8c8d;
    margin: 0;
}

.ai-track-player {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 15px;
}

.ai-track-info {
    margin-bottom: 15px;
}

.ai-track-info h4 {
    margin: 0 0 5px 0;
    color: #2c3e50;
}

.ai-track-info p {
    margin: 0;
    font-size: 0.9em;
    color: #7f8c8d;
}

.ai-player-controls {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.ai-player-progress {
    display: flex;
    align-items: center;
    gap: 10px;
}

.progress-bar {
    flex-grow: 1;
    height: 6px;
    background-color: #ecf0f1;
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background-color: #3498db;
    width: 0%;
    transition: width 0.3s ease;
}

.ai-volume-control {
    display: flex;
    align-items: center;
    gap: 10px;
}

.breathing-animation {
    width: 100px;
    height: 100px;
    background-color: #3498db;
    border-radius: 50%;
    margin: 20px auto;
    transition: transform 4s ease, background-color 4s ease;
}

.breathing-animation.inhale {
    transform: scale(1.5);
    background-color: #2ecc71;
}

.breathing-animation.exhale {
    transform: scale(1);
    background-color: #3498db;
}

.stretch-timer {
    font-size: 2em;
    margin: 10px 0;
    font-weight: 600;
    color: #2c3e50;
}

.stretch-controls {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 15px;
}

.stretch-btn {
    background-color: #ecf0f1;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    font-size: 1.2em;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.stretch-btn:hover {
    background-color: #d5dbdb;
}

.mindfulness-timer {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.timer-display {
    font-size: 2.5em;
    font-weight: 600;
    color: #2c3e50;
    margin: 10px 0;
}

.timer-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.time-btn {
    background-color: #ecf0f1;
    border: none;
    padding: 6px 12px;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.time-btn:hover {
    background-color: #d5dbdb;
}

.time-btn.active {
    background-color: #3498db;
    color: white;
}

/* Model selector styles */
.model-selector {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 15px;
    padding: 10px;
    background: rgba(52, 152, 219, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(52, 152, 219, 0.2);
}

.model-selector label {
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.model-select {
    padding: 5px 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background: white;
    font-size: 0.9em;
    cursor: pointer;
}

.model-status {
    font-size: 0.8em;
    padding: 3px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.model-status.online {
    background: #2ecc71;
    color: white;
}

.model-status.offline {
    background: #3498db;
    color: white;
}

.model-status.checking {
    background: #f39c12;
    color: white;
}

.model-status.error {
    background: #e74c3c;
    color: white;
}

.chat-container {
    background-color: #0d1117;
    border-radius: 16px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 650px;
    margin-top: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25),
                0 0 40px rgba(52, 152, 219, 0.1),
                inset 0 0 20px rgba(52, 152, 219, 0.05);
    border: 1px solid rgba(52, 152, 219, 0.3);
    position: relative;
}

.chat-messages {
    flex-grow: 1;
    overflow-y: auto;
    padding: 25px;
    display: flex;
    flex-direction: column;
    gap: 18px;
    scroll-behavior: smooth;
    background: linear-gradient(to bottom,
                rgba(13, 17, 23, 0.99),
                rgba(13, 17, 23, 0.95)),
                url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0zNiAxOGMwLTkuOTQtOC4wNi0xOC0xOC0xOFYyYzguODQgMCAxNiA3LjE2IDE2IDE2IDAgOC44NC03LjE2IDE2LTE2IDE2djJjOS45NCAwIDE4LTguMDYgMTgtMTgiIGZpbGw9IiMxYTFhMWEiLz48L2c+PC9zdmc+');
    position: relative;
}

.chat-messages:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: linear-gradient(to bottom, rgba(13, 17, 23, 1) 0%, rgba(13, 17, 23, 0) 100%);
    pointer-events: none;
    z-index: 1;
}

.chat-messages:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: linear-gradient(to top, rgba(13, 17, 23, 1) 0%, rgba(13, 17, 23, 0) 100%);
    pointer-events: none;
    z-index: 1;
}

.message {
    display: flex;
    position: relative;
    z-index: 2;
}

.message-avatar {
    width: 38px;
    height: 38px;
    border-radius: 50%;
    margin-right: 12px;
    background-size: cover;
    background-position: center;
    flex-shrink: 0;
    position: relative;
}

.ai-message .message-avatar {
    background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxjaXJjbGUgY3g9IjEwMCIgY3k9IjEwMCIgcj0iMTAwIiBmaWxsPSIjMDU0QThCIi8+CjxwYXRoIGQ9Ik02NSA2NUgxMzVWMTM1SDY1VjY1WiIgZmlsbD0iIzA1NEE4QiIgc3Ryb2tlPSIjOEVDNUZDIiBzdHJva2Utd2lkdGg9IjQiLz4KPHBhdGggZD0iTTY1IDY1TDEzNSAxMzUiIHN0cm9rZT0iIzhFQzVGQyIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxwYXRoIGQ9Ik0xMzUgNjVMNjUgMTM1IiBzdHJva2U9IiM4RUM1RkMiIHN0cm9rZS13aWR0aD0iNCIvPgo8Y2lyY2xlIGN4PSIxMDAiIGN5PSIxMDAiIHI9IjE1IiBmaWxsPSIjOEVDNUZDIi8+Cjwvc3ZnPgo=');
    border: 2px solid rgba(52, 152, 219, 0.5);
    box-shadow: 0 0 10px rgba(52, 152, 219, 0.3);
}

.user-message .message-avatar {
    background-image: url('https://via.placeholder.com/40');
    border: 2px solid rgba(46, 204, 113, 0.5);
    box-shadow: 0 0 10px rgba(46, 204, 113, 0.3);
    order: 1;
    margin-right: 0;
    margin-left: 12px;
}

.message-avatar::after {
    content: '';
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #2ecc71;
    border: 2px solid #0d1117;
}

.ai-message .message-avatar::after {
    background: #3498db;
}

.ai-message {
    max-width: 80%;
}

.user-message {
    align-self: flex-end;
    flex-direction: row-reverse;
}

.message-bubble {
    padding: 14px 18px;
    border-radius: 18px;
    line-height: 1.5;
    position: relative;
    transition: all 0.3s ease;
    max-width: calc(100% - 50px);
}

.ai-message .message-bubble {
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.15) 0%, rgba(52, 152, 219, 0.05) 100%);
    color: #e6f7ff;
    border: 1px solid rgba(52, 152, 219, 0.3);
    border-top-left-radius: 4px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1), 0 0 5px rgba(52, 152, 219, 0.1);
}

.user-message .message-bubble {
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.15) 0%, rgba(46, 204, 113, 0.05) 100%);
    color: #e6fff2;
    border: 1px solid rgba(46, 204, 113, 0.3);
    border-top-right-radius: 4px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1), 0 0 5px rgba(46, 204, 113, 0.1);
}

.message:hover .message-bubble {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15), 0 0 10px rgba(52, 152, 219, 0.2);
}

.message-time {
    font-size: 0.7em;
    color: rgba(255, 255, 255, 0.5);
    position: absolute;
    bottom: -20px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.ai-message .message-time {
    left: 0;
}

.user-message .message-time {
    right: 0;
}

.message:hover .message-time {
    opacity: 1;
}

.thinking {
    align-self: flex-start;
    width: fit-content;
    transition: opacity 0.3s ease;
}

.thinking .message-bubble {
    padding: 10px 16px;
}

/* Message content styles */
.message-content {
    transition: opacity 0.5s ease;
    font-size: 0.95rem;
    line-height: 1.5;
}

.message-content p {
    margin: 0 0 10px 0;
}

.message-content p:last-child {
    margin-bottom: 0;
}

/* Code block styling */
.message-content pre {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(52, 152, 219, 0.3);
    border-radius: 6px;
    padding: 12px;
    margin: 10px 0;
    overflow-x: auto;
    position: relative;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 0.85rem;
    color: #e6f7ff;
}

.message-content pre::before {
    content: 'code';
    position: absolute;
    top: -10px;
    left: 10px;
    background: #0d1117;
    padding: 0 8px;
    font-size: 0.7rem;
    color: rgba(52, 152, 219, 0.8);
    border-radius: 10px;
    border: 1px solid rgba(52, 152, 219, 0.3);
}

.message-content code {
    font-family: 'Consolas', 'Monaco', monospace;
    background: rgba(52, 152, 219, 0.1);
    padding: 2px 5px;
    border-radius: 3px;
    font-size: 0.85em;
    color: #8ec5fc;
}

/* Text effect styles */
.large-emoji {
    font-size: 1.5em;
    display: inline-block;
    margin: 0 2px;
    vertical-align: middle;
    transform-origin: center;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.highlight-text {
    background: linear-gradient(120deg, rgba(52, 152, 219, 0.2) 0%, rgba(52, 152, 219, 0.1) 100%);
    border-radius: 3px;
    padding: 0 3px;
    font-weight: 500;
    color: #8ec5fc;
}

.advice-heading {
    font-weight: 600;
    color: #2ecc71;
    display: block;
    margin-top: 10px;
    margin-bottom: 5px;
}

.list-item {
    display: block;
    margin: 5px 0;
    padding-left: 8px;
    border-left: 2px solid #3498db;
}

.typing-indicator {
    display: flex;
    gap: 5px;
    padding: 5px;
    align-items: center;
    height: 20px;
}

.typing-indicator span {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #3498db;
    opacity: 0.6;
    animation: typingBounce 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
    animation-delay: 0s;
}

.typing-indicator span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typingBounce {
    0%, 60%, 100% { transform: translateY(0); }
    30% { transform: translateY(-6px); }
}

/* Chat Controls */
.chat-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 18px;
    background-color: rgba(13, 17, 23, 0.9);
    border-top: 1px solid rgba(52, 152, 219, 0.2);
    position: relative;
    z-index: 5;
}

.chat-actions {
    display: flex;
    gap: 10px;
}

.chat-action-btn {
    background: rgba(52, 152, 219, 0.1);
    border: 1px solid rgba(52, 152, 219, 0.3);
    border-radius: 8px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.chat-action-btn:hover {
    background: rgba(52, 152, 219, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.action-icon {
    font-size: 1.2em;
}

.chat-status {
    font-size: 0.85em;
    color: rgba(255, 255, 255, 0.6);
}

.save-status {
    display: inline-block;
    transition: opacity 0.5s ease;
    opacity: 0;
}

.save-status.show {
    opacity: 1;
}

.chat-input {
    display: flex;
    padding: 18px;
    background-color: rgba(13, 17, 23, 0.8);
    border-top: 1px solid rgba(52, 152, 219, 0.2);
    position: relative;
    z-index: 5;
    backdrop-filter: blur(10px);
}

.chat-input::before {
    content: '';
    position: absolute;
    top: -1px;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(to right,
                transparent,
                rgba(52, 152, 219, 0.5),
                transparent);
}

.chat-input textarea {
    flex-grow: 1;
    padding: 14px 18px;
    background-color: rgba(255, 255, 255, 0.05);
    color: #e6f7ff;
    border: 1px solid rgba(52, 152, 219, 0.3);
    border-radius: 20px;
    resize: none;
    font-family: 'Poppins', sans-serif;
    height: 24px;
    max-height: 120px;
    transition: all 0.3s ease;
    box-shadow: 0 0 20px rgba(52, 152, 219, 0.05);
}

.chat-input textarea::placeholder {
    color: rgba(255, 255, 255, 0.3);
}

.chat-input textarea:focus {
    outline: none;
    border-color: rgba(52, 152, 219, 0.8);
    background-color: rgba(52, 152, 219, 0.1);
    height: 60px;
    box-shadow: 0 0 15px rgba(52, 152, 219, 0.2);
}

.chat-input button {
    margin-left: 12px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
    position: relative;
    overflow: hidden;
}

.chat-input button::before {
    content: '→';
    font-size: 1.5em;
    position: relative;
    z-index: 2;
}

.chat-input button::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
    z-index: 1;
}

.chat-input button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.chat-input button:active {
    transform: translateY(1px);
    box-shadow: 0 2px 10px rgba(52, 152, 219, 0.3);
}

.chat-suggestions {
    padding: 15px 18px;
    background-color: rgba(13, 17, 23, 0.7);
    border-top: 1px solid rgba(52, 152, 219, 0.1);
    backdrop-filter: blur(5px);
}

.chat-suggestions p {
    margin: 0 0 10px 0;
    font-size: 0.85em;
    color: rgba(255, 255, 255, 0.6);
    letter-spacing: 0.5px;
    text-transform: uppercase;
}

.suggestion-chips {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.suggestion-chip {
    background-color: rgba(52, 152, 219, 0.1);
    border: 1px solid rgba(52, 152, 219, 0.3);
    border-radius: 20px;
    padding: 8px 15px;
    font-size: 0.85em;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.suggestion-chip::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(transparent, rgba(52, 152, 219, 0.1), transparent);
    transform: rotate(30deg);
    transition: transform 0.5s ease;
}

.suggestion-chip:hover {
    background-color: rgba(52, 152, 219, 0.2);
    border-color: rgba(52, 152, 219, 0.5);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.2);
}

.suggestion-chip:hover::after {
    transform: translateX(100%) rotate(30deg);
}

.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1100;
    justify-content: center;
    align-items: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background-color: #fff;
    padding: 30px;
    border-radius: 12px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
}

/* Chat History Modal Styles */
.chat-history-modal {
    max-width: 900px;
    width: 90%;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
}

.chat-history-search {
    display: flex;
    margin-bottom: 15px;
    gap: 10px;
}

.chat-history-search input {
    flex-grow: 1;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 0.9em;
}

.chat-history-search button {
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 0 15px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.chat-history-search button:hover {
    background-color: #2980b9;
}

.chat-history-container {
    display: flex;
    gap: 20px;
    height: 400px;
    overflow: hidden;
}

.chat-history-list {
    width: 40%;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow-y: auto;
    background-color: #f9f9f9;
}

.chat-history-item {
    padding: 15px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.chat-history-item:hover {
    background-color: #f0f4f8;
}

.chat-history-item.selected {
    background-color: rgba(52, 152, 219, 0.1);
    border-left: 3px solid #3498db;
}

.chat-history-item-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
}

.chat-history-item-title {
    font-weight: 600;
    color: #2c3e50;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 70%;
}

.chat-history-item-date {
    font-size: 0.8em;
    color: #7f8c8d;
}

.chat-history-item-preview {
    font-size: 0.85em;
    color: #7f8c8d;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.chat-history-item-meta {
    display: flex;
    justify-content: space-between;
    font-size: 0.75em;
    color: #95a5a6;
    margin-top: 5px;
}

.chat-history-preview {
    width: 60%;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow-y: auto;
    background-color: #fff;
    padding: 15px;
}

.preview-placeholder {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #95a5a6;
    font-style: italic;
}

.preview-message {
    padding: 10px;
    margin-bottom: 10px;
    border-radius: 8px;
}

.preview-message.ai {
    background-color: #f0f4f8;
    border-left: 3px solid #3498db;
}

.preview-message.user {
    background-color: #f8f9fa;
    border-left: 3px solid #2ecc71;
}

.preview-message-header {
    font-weight: 600;
    margin-bottom: 5px;
    color: #2c3e50;
}

.preview-message-content {
    font-size: 0.9em;
    color: #34495e;
}

.chat-history-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
    gap: 10px;
}

.chat-history-actions button {
    padding: 8px 15px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.chat-history-actions button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

#loadSelectedChatBtn {
    background-color: #3498db;
    color: white;
}

#loadSelectedChatBtn:hover:not(:disabled) {
    background-color: #2980b9;
}

#deleteSelectedChatBtn, #deleteAllChatsBtn {
    background-color: #e74c3c;
    color: white;
}

#deleteSelectedChatBtn:hover:not(:disabled), #deleteAllChatsBtn:hover:not(:disabled) {
    background-color: #c0392b;
}

#exportSelectedChatBtn {
    background-color: #2ecc71;
    color: white;
}

#exportSelectedChatBtn:hover:not(:disabled) {
    background-color: #27ae60;
}

.empty-history-message {
    padding: 20px;
    text-align: center;
    color: #95a5a6;
    font-style: italic;
}

.preview-more-messages {
    text-align: center;
    padding: 10px;
    font-size: 0.85em;
    color: #95a5a6;
    font-style: italic;
    background-color: #f9f9f9;
    border-radius: 8px;
    margin-top: 10px;
}

.close-modal {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 1.5em;
    cursor: pointer;
    color: #7f8c8d;
}

.close-modal:hover {
    color: #2c3e50;
}

.profile-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-top: 15px;
}

.profile-avatar-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

#profileAvatarPreview {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #ecf0f1;
}

#changeAvatarBtn {
    background-color: #f0f4f8;
    border: none;
    padding: 8px 15px;
    border-radius: 20px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

#changeAvatarBtn:hover {
    background-color: #dde4eb;
}

.profile-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-right: 10px;
}

@media (max-width: 768px) {
    .main-nav {
        padding: 0 15px; /* Reduce padding on smaller screens */
    }

    .main-nav.sidebar {
        width: 100%;
        transform: translateX(-100%);
    }

    .main-nav.sidebar.right {
        transform: translateX(100%);
    }

    .main-nav.sidebar.active {
        transform: translateX(0);
    }

    .nav-toggle-btn {
        display: flex;
    }

    .page-container.nav-sidebar {
        margin-top: 0;
        margin-left: 0;
    }

    .page-container.nav-sidebar.right {
        margin-right: 0;
    }

    .container {
        padding: 20px;
    }

    .activities-grid {
        grid-template-columns: 1fr;
    }

    .profile-details {
        grid-template-columns: 1fr;
    }

    .nav-links li {
        margin: 0 3px; /* Reduce margins between nav items */
    }

    .nav-item {
        padding: 6px 8px; /* Reduce padding on nav items */
        font-size: 0.9em;
    }

    .logo span {
        font-size: 1rem; /* Smaller logo text */
    }

    .nav-position-toggle button {
        font-size: 0.7em; /* Smaller button text */
        padding: 4px 8px; /* Smaller button */
    }

    .profile-btn {
        width: 32px; /* Smaller profile button */
        height: 32px;
        margin-left: 5px; /* Less margin */
    }
}

/* Extra small devices */
@media (max-width: 480px) {
    .main-nav {
        padding: 0 10px; /* Even less padding */
    }

    .logo-icon {
        height: 30px; /* Smaller logo icon */
    }

    .nav-links {
        justify-content: flex-start; /* Left align nav links */
    }

    .nav-item {
        padding: 5px 6px; /* Smaller padding */
        font-size: 0.8em; /* Smaller font */
    }

    .nav-position-toggle {
        margin: 0 5px; /* Less margin */
    }
}

header {
    margin-bottom: 30px;
}

header h1 {
    color: #2c3e50;
    font-weight: 600;
    font-size: 2.2em;
    margin-bottom: 5px;
}

.ai-badge {
    background-color: #3498db;
    color: white;
    font-size: 0.5em;
    padding: 3px 8px;
    border-radius: 5px;
    vertical-align: middle;
    margin-left: 5px;
}

header p {
    color: #7f8c8d;
    font-size: 1.1em;
}

.input-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 30px;
}

.input-group {
    text-align: left;
}

.input-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 400;
    color: #34495e;
}

.input-group input[type="range"],
.input-group input[type="number"] {
    width: calc(100% - 60px); /* Adjust width if span is next to it */
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 6px;
    box-sizing: border-box;
    font-size: 1em;
}
.input-group input[type="number"] {
    width: 100%;
}

.input-group input[type="range"] {
    accent-color: #3498db;
    display: inline-block; /* For alignment with span */
    vertical-align: middle;
}

.input-group span {
    display: inline-block;
    margin-left: 10px;
    font-weight: 600;
    color: #3498db;
    vertical-align: middle;
    min-width: 50px; /* Ensure consistent spacing */
}

#predictButton {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 12px 25px;
    font-size: 1.1em;
    font-weight: 600;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    margin-top: 10px;
}

#predictButton:hover {
    background-color: #2980b9;
}

.result-area {
    background-color: #f9f9f9;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #eee;
    margin-top: 20px;
}

.result-area h2 {
    color: #2c3e50;
    margin-top: 0;
    font-size: 1.5em;
}

#predictionOutput p {
    font-size: 1.1em;
    line-height: 1.6;
    color: #555;
}

#predictionOutput p strong {
    color: #34495e;
}

#stressEmoji {
    font-size: 1.5em;
}

#loadingIndicator {
    text-align: center;
    padding: 20px;
}

.spinner {
    border: 6px solid #f3f3f3; /* Light grey */
    border-top: 6px solid #3498db; /* Blue */
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 10px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.stress-meter-container {
    margin: 20px 0;
    text-align: center;
}

.stress-meter {
    height: 20px;
    width: 100%;
    background-color: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 5px;
}

#stressMeterFill {
    height: 100%;
    background: linear-gradient(to right, #4CAF50, #FFEB3B, #FF9800, #F44336);
    width: 0;
    transition: width 1s ease-in-out;
}

.stress-labels {
    display: flex;
    justify-content: space-between;
    font-size: 0.8em;
    color: #666;
}

.personalized-tips {
    margin-top: 20px;
    text-align: left;
    background-color: rgba(255, 255, 255, 0.7);
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #3498db;
}

.personalized-tips h3 {
    margin-top: 0;
    color: #2c3e50;
    font-size: 1.2em;
}

.personalized-tips ul {
    margin: 10px 0 0 0;
    padding-left: 20px;
}

.personalized-tips li {
    margin-bottom: 8px;
    line-height: 1.4;
}

.history-section {
    margin-top: 30px;
    background-color: #f9f9f9;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #eee;
}

.chart-container {
    margin: 20px 0;
    height: 300px;
}

.history-list {
    margin-top: 20px;
    max-height: 200px;
    overflow-y: auto;
    padding: 10px;
    background-color: white;
    border-radius: 8px;
    border: 1px solid #eee;
}

.history-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.history-item:last-child {
    border-bottom: none;
}

.history-date {
    font-weight: 600;
    color: #2c3e50;
}

.history-level {
    padding: 3px 10px;
    border-radius: 20px;
    font-size: 0.9em;
}

.level-low {
    background-color: #e6ffed;
    color: #4CAF50;
}

.level-moderate {
    background-color: #fff9e6;
    color: #FF9800;
}

.level-high {
    background-color: #fff0e6;
    color: #FF9800;
}

.level-very-high {
    background-color: #ffe6e6;
    color: #F44336;
}

.save-button {
    background-color: #2ecc71;
    color: white;
    border: none;
    padding: 10px 20px;
    font-size: 1em;
    border-radius: 6px;
    cursor: pointer;
    margin-top: 15px;
    transition: background-color 0.3s ease;
}

.save-button:hover {
    background-color: #27ae60;
}

.clear-button {
    background-color: #e74c3c;
    color: white;
    border: none;
    padding: 8px 16px;
    font-size: 0.9em;
    border-radius: 6px;
    cursor: pointer;
    margin-top: 10px;
    transition: background-color 0.3s ease;
}

.clear-button:hover {
    background-color: #c0392b;
}

body.stress-low { background-color: #e6ffed; } /* Light Green */
body.stress-moderate { background-color: #fff9e6; } /* Light Yellow */
body.stress-high { background-color: #fff0e6; } /* Light Orange */
body.stress-very-high { background-color: #ffe6e6; } /* Light Red */

.slide-in-left {
    animation: slideInLeft 0.3s forwards;
}

.slide-in-right {
    animation: slideInRight 0.3s forwards;
}

.slide-out-left {
    animation: slideOutLeft 0.3s forwards;
}

.slide-out-right {
    animation: slideOutRight 0.3s forwards;
}

@keyframes slideInLeft {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

@keyframes slideOutLeft {
    from { transform: translateX(0); }
    to { transform: translateX(-100%); }
}

@keyframes slideOutRight {
    from { transform: translateX(0); }
    to { transform: translateX(100%); }
}

.welcome-message {
    text-align: center;
    padding: 18px;
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.1) 0%, rgba(52, 152, 219, 0.05) 100%);
    border-radius: 12px;
    margin-bottom: 20px;
    animation: fadeIn 0.8s;
    border: 1px solid rgba(52, 152, 219, 0.3);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1), 0 0 20px rgba(52, 152, 219, 0.1);
    position: relative;
    overflow: hidden;
}

.welcome-message::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg,
                transparent,
                rgba(52, 152, 219, 0.1),
                transparent);
    transform: translateX(-100%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.welcome-message p {
    margin: 0;
    font-weight: 500;
    color: #e6f7ff;
    font-size: 1.05rem;
    letter-spacing: 0.3px;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.avatar-options {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.avatar-selection {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    justify-content: center;
    margin-top: 15px;
}

.avatar-option {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    cursor: pointer;
    object-fit: cover;
    border: 3px solid transparent;
    transition: all 0.3s ease;
}

.avatar-option:hover, .avatar-option.selected {
    border-color: #3498db;
    transform: scale(1.1);
}

.avatar-selection-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1200;
    transition: opacity 0.3s ease;
}

.avatar-selection-modal.show {
    display: flex;
}

.avatar-modal-content {
    background-color: #fff;
    padding: 20px;
    border-radius: 12px;
    width: 90%;
    max-width: 440px;
    position: relative;
    max-height: 80vh;
    overflow-y: auto;
}

.avatar-modal-content h3 {
    margin-top: 0;
    margin-bottom: 15px;
    text-align: center;
}

.modal-close-btn {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 1.5em;
    cursor: pointer;
    color: #7f8c8d;
    transition: color 0.2s ease;
}

.modal-close-btn:hover {
    color: #e74c3c;
>>>>>>> 513d52fe8101d585a5030c28307a3be39001bf60
}