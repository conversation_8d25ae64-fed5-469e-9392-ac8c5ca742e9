const express = require('express');
const cors = require('cors');
const axios = require('axios');
let ytdl;
// Try to use the more reliable @distube/ytdl-core first, fallback to regular ytdl-core
try {
    ytdl = require('@distube/ytdl-core');
    console.log('Using @distube/ytdl-core for better YouTube support');
} catch (error) {
    ytdl = require('ytdl-core');
    console.log('Using regular ytdl-core');
}
const youtubeSearchApi = require('youtube-search-api');
const { exec, spawn } = require('child_process');
const { promisify } = require('util');
const fs = require('fs');
const path = require('path');
const app = express();
const port = 3000;

// Promisify exec for easier use
const execAsync = promisify(exec);

// Check if Python is available
exec('python --version', (error, stdout, stderr) => {
    if (error) {
        console.warn('Python not found. Python-based YouTube audio features will be disabled.');
        console.warn('Install Python and required packages for better audio support:');
        console.warn('pip install yt-dlp pytube ffmpeg-python requests');
    } else {
        console.log(`Python detected: ${stdout.trim()}`);
        // Check for required Python packages
        exec('python -c "import sys; print(\'yt_dlp\' in sys.modules or \'pytube\' in sys.modules)"',
            (err, out) => {
                if (err || out.trim() !== 'True') {
                    console.warn('Required Python packages not found. Install with:');
                    console.warn('pip install yt-dlp pytube ffmpeg-python requests');
                } else {
                    console.log('Python YouTube packages detected');
                }
            }
        );
    }
});

// Middleware
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.static('.'));

// Configuration for Ollama
const OLLAMA_BASE_URL = 'http://localhost:11434';
const MODEL_NAME = 'llama3.2:1b'; // Using the smaller 1B parameter version of Llama 3.2

// Helper function to enhance system prompt with stress data
function enhanceSystemPrompt(messages, stressData) {
    if (!stressData || !stressData.lastPrediction) return messages;

    // Find the system message
    const systemMessageIndex = messages.findIndex(msg => msg.role === 'system');
    if (systemMessageIndex === -1) {
        // If no system message exists, create one with stress data
        const stressInfo = createStressInfoPrompt(stressData);
        messages.unshift({
            role: 'system',
            content: stressInfo
        });
    } else {
        // Enhance existing system message with stress data
        const stressInfo = createStressInfoPrompt(stressData);
        messages[systemMessageIndex].content = `${stressInfo}\n\n${messages[systemMessageIndex].content}`;
    }

    return messages;
}

// Create a detailed prompt about the student's stress data
function createStressInfoPrompt(stressData) {
    const { lastPrediction, studentData } = stressData;

    let prompt = `You are a helpful AI assistant for students. The student has recently taken a stress assessment with the following results:`;

    if (lastPrediction) {
        prompt += `\n- Stress Level: ${lastPrediction.level}`;
        prompt += `\n- Stress Score: ${lastPrediction.score}/100`;

        if (lastPrediction.advice) {
            prompt += `\n- Advice Given: "${lastPrediction.advice}"`;
        }

        if (lastPrediction.tips && lastPrediction.tips.length > 0) {
            prompt += `\n- Tips Provided:`;
            lastPrediction.tips.forEach(tip => {
                prompt += `\n  * ${tip}`;
            });
        }
    }

    if (studentData) {
        prompt += `\n\nThe student's current situation:`;
        if (studentData.sleep !== undefined) prompt += `\n- Sleep: ${studentData.sleep} hours`;
        if (studentData.homeworkLoad !== undefined) prompt += `\n- Homework Load (0-10): ${studentData.homeworkLoad}`;
        if (studentData.upcomingExams !== undefined) prompt += `\n- Upcoming Exams: ${studentData.upcomingExams}`;
        if (studentData.socialInteraction !== undefined) prompt += `\n- Social Interaction (0-5): ${studentData.socialInteraction}`;
        if (studentData.extracurriculars !== undefined) prompt += `\n- Extracurricular Activities (0-5): ${studentData.extracurriculars}`;
        if (studentData.nutrition !== undefined) prompt += `\n- Nutrition Quality (0-5): ${studentData.nutrition}`;
        if (studentData.screenTime !== undefined) prompt += `\n- Screen Time: ${studentData.screenTime} hours`;
    }

    prompt += `\n\nKeep this information in mind when responding to the student. Be empathetic, supportive, and provide relevant advice based on their stress level and situation. Your responses should be concise, positive, and actionable.`;

    return prompt;
}

// API endpoint for chat completions
app.post('/api/chat/completions', async (req, res) => {
    try {
        const { messages, json, stressData } = req.body;

        // Enhanced messages with stress data if available
        const enhancedMessages = stressData ? enhanceSystemPrompt([...messages], stressData) : messages;

        // Log the enhanced messages for debugging
        console.log('Enhanced messages with stress data:', JSON.stringify(enhancedMessages, null, 2));

        // Format messages for Ollama API
        const ollamaRequest = {
            model: MODEL_NAME,
            messages: enhancedMessages,
            stream: false,
            options: {
                temperature: 0.7,
                top_p: 0.9
            }
        };

        // Call Ollama API
        const response = await axios.post(`${OLLAMA_BASE_URL}/api/chat`, ollamaRequest);

        // Format response to match expected format
        const formattedResponse = {
            content: response.data.message.content,
            role: response.data.message.role
        };

        res.json(formattedResponse);
    } catch (error) {
        console.error('Error calling Ollama API:', error.message);
        res.status(500).json({
            error: 'Failed to get response from Ollama',
            details: error.message
        });
    }
});

// YouTube API endpoints
app.get('/api/youtube/search', async (req, res) => {
    try {
        const { query = 'relaxing music', limit = 15, filter = 'music' } = req.query;

        // Add category filter for better music results
        let searchQuery = query;
        if (filter === 'music' && !query.includes('music')) {
            searchQuery = `${query} music`;
        }

        console.log(`Searching YouTube for: "${searchQuery}" with limit ${limit}`);
        const searchResults = await youtubeSearchApi.GetListByKeyword(searchQuery, false, limit);

        // Log the number of results found
        if (searchResults && searchResults.items) {
            console.log(`Found ${searchResults.items.length} YouTube results`);
        }

        res.json(searchResults);
    } catch (error) {
        console.error('Error searching YouTube:', error);
        res.status(500).json({
            error: 'Failed to search YouTube',
            details: error.message
        });
    }
});

// Extract video ID from various YouTube URL formats
app.get('/api/youtube/extract-id', (req, res) => {
    try {
        const { url } = req.query;

        if (!url) {
            return res.status(400).json({ error: 'URL parameter is required' });
        }

        // Check if it's already a video ID (11 characters)
        if (/^[a-zA-Z0-9_-]{11}$/.test(url)) {
            return res.json({ videoId: url });
        }

        // Try to extract using ytdl-core
        const videoId = ytdl.getVideoID(url);
        return res.json({ videoId });
    } catch (error) {
        console.error('Error extracting YouTube video ID:', error);
        res.status(400).json({
            error: 'Invalid YouTube URL',
            details: error.message
        });
    }
});

app.get('/api/youtube/video-info/:videoId', async (req, res) => {
    try {
        const { videoId } = req.params;
        console.log('Fetching audio info for YouTube video:', videoId);

        // Try Python-based extraction first if available
        try {
            const pythonResult = await execAsync(`python youtube_audio.py --url ${videoId} --info`);
            if (pythonResult && pythonResult.stdout) {
                const audioInfo = JSON.parse(pythonResult.stdout);

                if (!audioInfo.error) {
                    console.log('Successfully extracted audio info using Python');
                    return res.json(audioInfo);
                }
            }
        } catch (pythonError) {
            console.warn('Python audio extraction failed, trying Node.js method:', pythonError.message);
        }

        // Fallback to Node.js ytdl-core with enhanced error handling
        let info;
        try {
            // Use getBasicInfo first for faster response and better error handling
            info = await ytdl.getBasicInfo(videoId);
        } catch (basicError) {
            console.warn('getBasicInfo failed, trying getInfo:', basicError.message);

            // If basic info fails, try full info with timeout
            const timeoutPromise = new Promise((_, reject) =>
                setTimeout(() => reject(new Error('YouTube info extraction timeout')), 15000)
            );

            info = await Promise.race([
                ytdl.getInfo(videoId),
                timeoutPromise
            ]);
        }

        // Check if the video is playable
        if (info.videoDetails.isPrivate) {
            throw new Error('This video is private and cannot be played');
        }

        if (info.videoDetails.isLiveContent && !info.videoDetails.isLiveNow) {
            throw new Error('This was a live stream and is no longer available');
        }

        // Log available formats for debugging
        console.log('Available formats:', info.formats.map(f => ({
            itag: f.itag,
            mimeType: f.mimeType,
            hasAudio: f.hasAudio,
            hasVideo: f.hasVideo,
            audioBitrate: f.audioBitrate
        })));

        // Prioritize audio-only formats for better performance
        const audioFormats = info.formats
            .filter(format => format.hasAudio)
            .sort((a, b) => {
                // Prioritize audio-only formats
                const aIsAudioOnly = a.hasAudio && !a.hasVideo;
                const bIsAudioOnly = b.hasAudio && !b.hasVideo;

                if (aIsAudioOnly && !bIsAudioOnly) return -1;
                if (!aIsAudioOnly && bIsAudioOnly) return 1;

                // Then sort by quality (higher bitrate first)
                return (b.audioBitrate || 0) - (a.audioBitrate || 0);
            });

        // Group formats by mime type for better browser compatibility
        const formatsByType = {
            mp4Audio: audioFormats.filter(f => f.mimeType && f.mimeType.includes('audio/mp4')),
            webmAudio: audioFormats.filter(f => f.mimeType && f.mimeType.includes('audio/webm')),
            otherAudio: audioFormats.filter(f =>
                f.mimeType &&
                f.mimeType.includes('audio/') &&
                !f.mimeType.includes('audio/mp4') &&
                !f.mimeType.includes('audio/webm')
            ),
            videoWithAudio: audioFormats.filter(f => f.hasVideo)
        };

        console.log('Formats by type:', {
            mp4Audio: formatsByType.mp4Audio.length,
            webmAudio: formatsByType.webmAudio.length,
            otherAudio: formatsByType.otherAudio.length,
            videoWithAudio: formatsByType.videoWithAudio.length
        });

        // Create a prioritized list of formats
        const prioritizedFormats = [
            ...formatsByType.mp4Audio,
            ...formatsByType.webmAudio,
            ...formatsByType.otherAudio,
            ...formatsByType.videoWithAudio
        ];

        // Extract only the necessary information
        const videoInfo = {
            videoId: videoId,
            title: info.videoDetails.title,
            author: info.videoDetails.author.name,
            lengthSeconds: info.videoDetails.lengthSeconds,
            viewCount: info.videoDetails.viewCount,
            thumbnails: info.videoDetails.thumbnails,
            description: info.videoDetails.description ? info.videoDetails.description.slice(0, 200) + '...' : '',
            category: info.videoDetails.category,
            isLive: info.videoDetails.isLiveContent,
            formats: prioritizedFormats.map(format => ({
                itag: format.itag,
                mimeType: format.mimeType,
                quality: format.quality,
                audioBitrate: format.audioBitrate,
                url: format.url,
                hasVideo: format.hasVideo,
                container: format.container
            }))
        };

        res.json(videoInfo);
    } catch (error) {
        console.error('Error getting audio info:', error);

        // Provide more specific error messages
        let errorMessage = 'Failed to get audio info';
        let statusCode = 500;

        if (error.message.includes('Video unavailable')) {
            errorMessage = 'This video is not available';
            statusCode = 404;
        } else if (error.message.includes('Could not extract functions')) {
            errorMessage = 'YouTube audio extraction temporarily unavailable. Please try again or use a different video.';
            statusCode = 503;
        } else if (error.message.includes('timeout')) {
            errorMessage = 'Request timed out. Please try again.';
            statusCode = 408;
        } else if (error.message.includes('private')) {
            errorMessage = 'This video is private and cannot be accessed';
            statusCode = 403;
        }

        res.status(statusCode).json({
            error: errorMessage,
            details: error.message,
            suggestion: 'Try using a different YouTube video or use the built-in relaxation music instead.'
        });
    }
});

// Add a proxy endpoint for YouTube audio to avoid CORS issues
app.get('/api/youtube/audio-proxy', async (req, res) => {
    try {
        const { url, videoId } = req.query;

        // Handle direct videoId input
        if (videoId && !url) {
            console.log('Getting audio stream for video ID:', videoId);

            try {
                // Get video info to find the best audio format
                const info = await ytdl.getInfo(videoId);

                // Find the best audio format
                const format = ytdl.chooseFormat(info.formats, {
                    quality: 'highestaudio',
                    filter: 'audioonly'
                });

                if (!format) {
                    throw new Error('No suitable audio format found');
                }

                // Stream the audio
                console.log('Streaming audio format:', {
                    itag: format.itag,
                    mimeType: format.mimeType,
                    audioBitrate: format.audioBitrate
                });

                // Set appropriate headers
                res.set('Content-Type', format.mimeType || 'audio/mp4');
                res.set('Accept-Ranges', 'bytes');
                res.set('Cache-Control', 'public, max-age=3600');

                // Create a stream and pipe it to the response
                const stream = ytdl(videoId, { format });
                stream.pipe(res);

                return;
            } catch (error) {
                console.error('Error streaming from video ID:', error);
                return res.status(500).json({
                    error: 'Failed to stream audio from video ID',
                    details: error.message
                });
            }
        }

        if (!url) {
            return res.status(400).json({ error: 'URL parameter is required' });
        }

        console.log('Proxying audio from:', url);

        // Forward the request to the actual audio URL
        const response = await axios({
            method: 'get',
            url: url,
            responseType: 'stream',
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            },
            // Add timeout to prevent hanging requests
            timeout: 15000
        });

        // Set appropriate headers
        res.set('Content-Type', response.headers['content-type']);
        if (response.headers['content-length']) {
            res.set('Content-Length', response.headers['content-length']);
        }
        res.set('Accept-Ranges', 'bytes');
        res.set('Cache-Control', 'public, max-age=3600');

        // Pipe the audio stream to the response
        response.data.pipe(res);
    } catch (error) {
        console.error('Error proxying audio:', error);
        res.status(500).json({
            error: 'Failed to proxy audio',
            details: error.message
        });
    }
});

// Add a direct streaming endpoint for YouTube audio
app.get('/api/youtube/stream/:videoId', async (req, res) => {
    try {
        const { videoId } = req.params;
        const { quality = 'highestaudio', format = 'mp4' } = req.query;

        console.log(`Streaming YouTube video ${videoId} with quality ${quality} and format preference ${format}`);

        // Try Python-based extraction first if available
        try {
            const pythonResult = await execAsync(`python youtube_audio.py --url ${videoId} --stream`);
            if (pythonResult && pythonResult.stdout) {
                const streamInfo = JSON.parse(pythonResult.stdout);

                if (streamInfo.success && streamInfo.url) {
                    console.log('Using Python-extracted direct stream URL');

                    // Proxy the stream to avoid CORS issues
                    const response = await axios({
                        method: 'get',
                        url: streamInfo.url,
                        responseType: 'stream',
                        headers: {
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                        },
                        timeout: 15000
                    });

                    // Set appropriate headers
                    res.set('Content-Type', streamInfo.mimeType || response.headers['content-type'] || 'audio/mp4');
                    if (response.headers['content-length']) {
                        res.set('Content-Length', response.headers['content-length']);
                    }
                    res.set('Accept-Ranges', 'bytes');
                    res.set('Cache-Control', 'public, max-age=3600');

                    // Pipe the audio stream to the response
                    response.data.pipe(res);
                    return;
                }
            }
        } catch (pythonError) {
            console.warn('Python extraction failed, falling back to Node.js method:', pythonError.message);
            // Continue with Node.js method
        }

        // Fallback to Node.js ytdl-core method with enhanced error handling
        let info;
        try {
            // Try getBasicInfo first for better error handling
            info = await ytdl.getBasicInfo(videoId);
        } catch (basicError) {
            console.warn('getBasicInfo failed for streaming, trying getInfo:', basicError.message);

            // If basic info fails, try full info with timeout
            const timeoutPromise = new Promise((_, reject) =>
                setTimeout(() => reject(new Error('YouTube streaming timeout')), 15000)
            );

            info = await Promise.race([
                ytdl.getInfo(videoId),
                timeoutPromise
            ]);
        }

        // Filter formats based on preferences
        let formatOptions = { quality };

        if (format === 'mp4') {
            formatOptions.filter = (format) => format.hasAudio && format.container === 'mp4';
        } else if (format === 'webm') {
            formatOptions.filter = (format) => format.hasAudio && format.container === 'webm';
        } else {
            formatOptions.filter = 'audioonly';
        }

        // Choose the best format
        let selectedFormat;
        try {
            selectedFormat = ytdl.chooseFormat(info.formats, formatOptions);
        } catch (formatError) {
            console.warn('Could not find format with specified options, falling back to any audio format');
            selectedFormat = ytdl.chooseFormat(info.formats, { quality: 'highestaudio' });
        }

        if (!selectedFormat) {
            throw new Error('No suitable audio format found');
        }

        console.log('Selected format for streaming:', {
            itag: selectedFormat.itag,
            mimeType: selectedFormat.mimeType,
            container: selectedFormat.container,
            audioBitrate: selectedFormat.audioBitrate
        });

        // Set appropriate headers
        res.set('Content-Type', selectedFormat.mimeType || 'audio/mp4');
        res.set('Accept-Ranges', 'bytes');
        res.set('Cache-Control', 'public, max-age=3600');

        // Stream the video
        const stream = ytdl(videoId, { format: selectedFormat });

        // Handle errors in the stream
        stream.on('error', (err) => {
            console.error('Stream error:', err);
            if (!res.headersSent) {
                res.status(500).json({ error: 'Streaming error', details: err.message });
            }
        });

        // Pipe the stream to the response
        stream.pipe(res);
    } catch (error) {
        console.error('Error streaming YouTube audio:', error);

        // Provide more specific error messages
        let errorMessage = 'Failed to stream YouTube audio';
        let statusCode = 500;

        if (error.message.includes('Video unavailable')) {
            errorMessage = 'This video is not available for streaming';
            statusCode = 404;
        } else if (error.message.includes('Could not extract functions')) {
            errorMessage = 'YouTube audio streaming temporarily unavailable. Please try again or use a different video.';
            statusCode = 503;
        } else if (error.message.includes('timeout')) {
            errorMessage = 'Streaming request timed out. Please try again.';
            statusCode = 408;
        } else if (error.message.includes('private')) {
            errorMessage = 'This video is private and cannot be streamed';
            statusCode = 403;
        }

        res.status(statusCode).json({
            error: errorMessage,
            details: error.message,
            suggestion: 'Try using a different YouTube video or use the built-in relaxation music instead.'
        });
    }
});

// Add an endpoint to validate YouTube URLs using Python
app.get('/api/youtube/validate', async (req, res) => {
    try {
        const { url } = req.query;

        if (!url) {
            return res.status(400).json({ error: 'URL parameter is required' });
        }

        // Try to validate using Python first
        try {
            const pythonResult = await execAsync(`python youtube_audio.py --url ${url} --validate`);
            if (pythonResult && pythonResult.stdout) {
                const validationResult = JSON.parse(pythonResult.stdout);
                return res.json(validationResult);
            }
        } catch (pythonError) {
            console.warn('Python validation failed, falling back to Node.js method:', pythonError.message);
        }

        // Fallback to Node.js validation
        try {
            // Extract video ID
            const videoId = ytdl.getVideoID(url);

            // Check if video exists and is playable
            const info = await ytdl.getBasicInfo(videoId);

            // Check if the video is playable
            if (info.videoDetails.isPrivate) {
                return res.json({ valid: false, error: 'This video is private and cannot be played' });
            }

            if (info.videoDetails.isLiveContent && !info.videoDetails.isLiveNow) {
                return res.json({ valid: false, error: 'This was a live stream and is no longer available' });
            }

            // Check if there are audio formats available
            const hasAudioFormats = info.formats.some(format => format.hasAudio);

            if (!hasAudioFormats) {
                return res.json({ valid: false, error: 'No audio formats available for this video' });
            }

            return res.json({
                valid: true,
                videoId: videoId,
                title: info.videoDetails.title,
                author: info.videoDetails.author.name,
                lengthSeconds: info.videoDetails.lengthSeconds
            });
        } catch (error) {
            return res.json({ valid: false, error: error.message });
        }
    } catch (error) {
        console.error('Error validating YouTube URL:', error);
        res.status(500).json({
            error: 'Failed to validate YouTube URL',
            details: error.message
        });
    }
});

// Start server
app.listen(port, () => {
    console.log(`Server running at http://localhost:${port}`);
    console.log(`Using Ollama model: ${MODEL_NAME}`);
});
