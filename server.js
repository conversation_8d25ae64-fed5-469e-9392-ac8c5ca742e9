const express = require('express');
const cors = require('cors');
const axios = require('axios');
let ytdl;
// Try to use the more reliable @distube/ytdl-core first, fallback to regular ytdl-core
try {
    ytdl = require('@distube/ytdl-core');
    console.log('Using @distube/ytdl-core for better YouTube support');
} catch (error) {
    ytdl = require('ytdl-core');
    console.log('Using regular ytdl-core');
}
const youtubeSearchApi = require('youtube-search-api');
const { exec } = require('child_process');
const { promisify } = require('util');
const app = express();
const port = 3000;

// Promisify exec for easier use
const execAsync = promisify(exec);

// Check if Python is available
exec('python --version', (error, stdout, stderr) => {
    if (error) {
        console.warn('Python not found. Python-based YouTube audio features will be disabled.');
        console.warn('Install Python and required packages for better audio support:');
        console.warn('pip install yt-dlp pytube ffmpeg-python requests');
    } else {
        console.log(`Python detected: ${stdout.trim()}`);
    }
});

// Middleware
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.static('.'));

// Configuration for AI models
const OLLAMA_BASE_URL = 'http://localhost:11434';
const LOCAL_MODEL_NAME = 'llama3.2:1b'; // Local Ollama model
const OPENROUTER_API_KEY = '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';
const OPENROUTER_MODEL = 'deepseek/deepseek-chat-v3-0324:free';
const OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';

// Model preference: 'auto', 'online', or 'offline'
let MODEL_PREFERENCE = 'auto';

// Function to make OpenRouter API request
async function makeOpenRouterRequest(messages) {
    try {
        console.log('Making request to OpenRouter API...');
        const response = await axios.post(`${OPENROUTER_BASE_URL}/chat/completions`, {
            model: OPENROUTER_MODEL,
            messages: messages,
            temperature: 0.7,
            max_tokens: 1000
        }, {
            headers: {
                'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
                'Content-Type': 'application/json',
                'HTTP-Referer': 'http://localhost:3000',
                'X-Title': 'Stress AI Assistant'
            },
            timeout: 15000
        });

        console.log('OpenRouter API response received');
        return {
            success: true,
            data: response.data,
            model: 'online'
        };
    } catch (error) {
        console.error('OpenRouter API error:', error.message);
        return {
            success: false,
            error: error.message,
            model: 'online'
        };
    }
}

// Function to make Ollama API request
async function makeOllamaRequest(messages) {
    try {
        console.log('Making request to local Ollama API...');
        const response = await axios.post(`${OLLAMA_BASE_URL}/api/chat`, {
            model: LOCAL_MODEL_NAME,
            messages: messages,
            stream: false,
            options: {
                temperature: 0.7,
                top_p: 0.9
            }
        }, {
            timeout: 15000
        });

        console.log('Ollama API response received');
        return {
            success: true,
            data: {
                choices: [{
                    message: {
                        content: response.data.message.content,
                        role: 'assistant'
                    }
                }],
                usage: response.data.usage || {}
            },
            model: 'offline'
        };
    } catch (error) {
        console.error('Ollama API error:', error.message);
        return {
            success: false,
            error: error.message,
            model: 'offline'
        };
    }
}

// Function to make AI request with fallback
async function makeAIRequest(messages, preference = MODEL_PREFERENCE) {
    let result;
    
    if (preference === 'online') {
        result = await makeOpenRouterRequest(messages);
    } else if (preference === 'offline') {
        result = await makeOllamaRequest(messages);
    } else {
        result = await makeOpenRouterRequest(messages);
        if (!result.success) {
            console.log('Online model failed, trying offline model...');
            result = await makeOllamaRequest(messages);
        }
    }
    
    return result;
}

// Function to generate fallback stress prediction
function generateFallbackStressPrediction(studentData) {
    let stressScore = 0;
    
    // Sleep factor
    if (studentData.sleep < 6) stressScore += 30;
    else if (studentData.sleep < 7) stressScore += 20;
    else if (studentData.sleep < 8) stressScore += 10;
    
    // Homework load factor
    stressScore += Math.min(studentData.homeworkLoad * 2.5, 25);
    
    // Exams factor
    stressScore += Math.min(studentData.upcomingExams * 5, 20);
    
    // Screen time factor
    if (studentData.screenTime > 8) stressScore += 15;
    else if (studentData.screenTime > 6) stressScore += 10;
    else if (studentData.screenTime > 4) stressScore += 5;
    
    // Reduce stress for positive factors
    stressScore -= studentData.socialInteraction * 2;
    stressScore -= studentData.extracurriculars * 1.5;
    stressScore -= studentData.nutrition * 2;
    
    // Ensure score is within bounds
    stressScore = Math.max(0, Math.min(100, Math.round(stressScore)));
    
    // Determine stress level and emoji
    let stressLevel, emoji;
    if (stressScore < 25) {
        stressLevel = "Low";
        emoji = "😊";
    } else if (stressScore < 50) {
        stressLevel = "Moderate";
        emoji = "😌";
    } else if (stressScore < 75) {
        stressLevel = "High";
        emoji = "😰";
    } else {
        stressLevel = "Very High";
        emoji = "😫";
    }
    
    // Generate advice
    let advice;
    if (stressScore < 25) {
        advice = "You're doing great! Keep maintaining your healthy habits.";
    } else if (stressScore < 50) {
        advice = "Consider taking short breaks and practicing relaxation techniques.";
    } else if (stressScore < 75) {
        advice = "Focus on stress management - prioritize sleep and reduce workload where possible.";
    } else {
        advice = "High stress detected. Please prioritize self-care and consider talking to someone.";
    }
    
    // Generate tips
    const tips = [];
    if (studentData.sleep < 7) tips.push("Aim for 7-9 hours of sleep per night");
    if (studentData.homeworkLoad > 7) tips.push("Break large tasks into smaller, manageable chunks");
    if (studentData.upcomingExams > 2) tips.push("Create a study schedule to spread out exam preparation");
    if (studentData.socialInteraction < 3) tips.push("Schedule time for social activities with friends");
    if (studentData.nutrition < 3) tips.push("Focus on eating regular, nutritious meals");
    if (studentData.screenTime > 6) tips.push("Take regular breaks from screens");
    
    // Ensure we have at least 3 tips
    while (tips.length < 3) {
        const generalTips = [
            "Practice deep breathing exercises",
            "Take short walks to clear your mind",
            "Stay hydrated throughout the day",
            "Listen to calming music",
            "Try meditation or mindfulness"
        ];
        const randomTip = generalTips[Math.floor(Math.random() * generalTips.length)];
        if (!tips.includes(randomTip)) {
            tips.push(randomTip);
        }
    }
    
    return {
        stressLevel,
        stressScore,
        advice,
        emoji,
        tips: tips.slice(0, 5)
    };
}

// Helper function to enhance system prompt with stress data
function enhanceSystemPrompt(messages, stressData) {
    if (!stressData || !stressData.lastPrediction) return messages;
    
    const systemMessageIndex = messages.findIndex(msg => msg.role === 'system');
    const stressInfo = createStressInfoPrompt(stressData);
    
    if (systemMessageIndex === -1) {
        messages.unshift({
            role: 'system',
            content: stressInfo
        });
    } else {
        messages[systemMessageIndex].content = `${stressInfo}\n\n${messages[systemMessageIndex].content}`;
    }
    
    return messages;
}

// Create stress info prompt
function createStressInfoPrompt(stressData) {
    const { lastPrediction, studentData } = stressData;
    let prompt = `You are a helpful AI assistant for students. The student has recently taken a stress assessment with the following results:`;
    
    if (lastPrediction) {
        prompt += `\n- Stress Level: ${lastPrediction.level}`;
        prompt += `\n- Stress Score: ${lastPrediction.score}/100`;
        if (lastPrediction.advice) {
            prompt += `\n- Advice Given: "${lastPrediction.advice}"`;
        }
    }
    
    if (studentData) {
        prompt += `\n\nThe student's current situation:`;
        if (studentData.sleep !== undefined) prompt += `\n- Sleep: ${studentData.sleep} hours`;
        if (studentData.homeworkLoad !== undefined) prompt += `\n- Homework Load (0-10): ${studentData.homeworkLoad}`;
        if (studentData.upcomingExams !== undefined) prompt += `\n- Upcoming Exams: ${studentData.upcomingExams}`;
    }
    
    prompt += `\n\nKeep this information in mind when responding to the student. Be empathetic, supportive, and provide relevant advice based on their stress level and situation.`;
    
    return prompt;
}

// API endpoint for chat completions
app.post('/api/chat/completions', async (req, res) => {
    try {
        const { messages, stressData, modelPreference } = req.body;
        const enhancedMessages = stressData ? enhanceSystemPrompt([...messages], stressData) : messages;
        const preference = modelPreference || MODEL_PREFERENCE;

        console.log(`Using model preference: ${preference}`);
        const result = await makeAIRequest(enhancedMessages, preference);

        if (result.success) {
            console.log(`AI response received from ${result.model} model`);
            const formattedResponse = {
                content: result.data.choices[0].message.content,
                role: result.data.choices[0].message.role,
                model_used: result.model,
                model_name: result.model === 'online' ? OPENROUTER_MODEL : LOCAL_MODEL_NAME
            };
            res.json(formattedResponse);
        } else {
            console.error('All AI models failed:', result.error);
            res.status(503).json({
                error: 'AI service temporarily unavailable',
                details: result.error,
                suggestion: 'Please try again later or check your internet connection'
            });
        }
    } catch (error) {
        console.error('Error in chat completions endpoint:', error);
        res.status(500).json({
            error: 'Failed to get AI response',
            details: error.message
        });
    }
});

// Dedicated endpoint for stress prediction with enhanced fallback
app.post('/api/stress/predict', async (req, res) => {
    try {
        const { studentData } = req.body;

        if (!studentData) {
            return res.status(400).json({
                error: 'Student data is required',
                details: 'Please provide student data for stress prediction'
            });
        }

        console.log('Stress prediction request received:', studentData);

        const systemPrompt = `You are a helpful AI assistant that predicts a student's daily stress level based on several factors. You also provide a brief, positive piece of advice and personalized tips. Be encouraging, concise, and provide actionable advice. Use a friendly, supportive tone.
Respond ONLY with a JSON object matching this schema:
{
  "stressLevel": "string (Low, Moderate, High, or Very High)",
  "stressScore": "number between 0 and 100, with 0 being no stress and 100 being maximum stress",
  "advice": "string (1-2 concise sentences of positive advice)",
  "emoji": "string (a single emoji representing the stress level or mood)",
  "tips": ["array of 3-5 brief, specific tips based on their inputs"]
}
Do not include any other text or explanations outside of the JSON object. Keep advice encouraging and actionable if possible.`;

        const userPrompt = `Predict the stress level for a student with the following daily factors:
- Sleep: ${studentData.sleep} hours
- Homework Load (0-10 scale): ${studentData.homeworkLoad}
- Number of Upcoming Exams: ${studentData.upcomingExams}
- Social Interaction (0-5 scale): ${studentData.socialInteraction}
- Extracurricular Activities (0-5 scale): ${studentData.extracurriculars}
- Nutrition Quality (0-5 scale): ${studentData.nutrition}
- Screen Time: ${studentData.screenTime} hours

Consider these factors together to determine stress level. For example, low sleep, high homework load, and many exams would indicate high stress. Good social interaction and nutrition might lower stress levels.`;

        // Try AI prediction with shorter timeout
        try {
            const result = await makeAIRequest([
                { role: "system", content: systemPrompt },
                { role: "user", content: userPrompt }
            ], 'auto');

            if (result.success) {
                console.log('AI stress prediction successful');

                try {
                    const jsonMatch = result.data.choices[0].message.content.match(/\{[\s\S]*\}/);
                    const jsonString = jsonMatch ? jsonMatch[0] : result.data.choices[0].message.content;
                    const prediction = JSON.parse(jsonString);

                    if (prediction.stressLevel && prediction.advice) {
                        return res.json({
                            success: true,
                            prediction: prediction,
                            source: 'ai',
                            model_used: result.model
                        });
                    } else {
                        throw new Error('Invalid AI response format');
                    }
                } catch (parseError) {
                    console.warn('Failed to parse AI response, using fallback');
                    throw parseError;
                }
            } else {
                throw new Error('AI prediction failed');
            }
        } catch (aiError) {
            console.log('AI prediction failed, using rule-based fallback:', aiError.message);

            const fallbackPrediction = generateFallbackStressPrediction(studentData);

            return res.json({
                success: true,
                prediction: fallbackPrediction,
                source: 'fallback',
                message: 'AI temporarily unavailable, using intelligent fallback system'
            });
        }

    } catch (error) {
        console.error('Error in stress prediction endpoint:', error);
        res.status(500).json({
            error: 'Failed to predict stress level',
            details: error.message
        });
    }
});

// Endpoint to get current model status
app.get('/api/model/status', async (req, res) => {
    try {
        console.log('Checking model status...');

        let onlineStatus = 'unknown';
        try {
            const testResponse = await makeOpenRouterRequest([{
                role: 'user',
                content: 'Test'
            }]);
            onlineStatus = testResponse.success ? 'available' : 'unavailable';
        } catch (error) {
            onlineStatus = 'unavailable';
        }

        let offlineStatus = 'unknown';
        try {
            const testResponse = await makeOllamaRequest([{
                role: 'user',
                content: 'Test'
            }]);
            offlineStatus = testResponse.success ? 'available' : 'unavailable';
        } catch (error) {
            offlineStatus = 'unavailable';
        }

        const statusResponse = {
            current_preference: MODEL_PREFERENCE,
            online_model: {
                name: OPENROUTER_MODEL,
                status: onlineStatus
            },
            offline_model: {
                name: LOCAL_MODEL_NAME,
                status: offlineStatus
            },
            available_preferences: ['auto', 'online', 'offline']
        };

        res.json(statusResponse);
    } catch (error) {
        console.error('Error checking model status:', error);
        res.status(500).json({
            error: 'Failed to check model status',
            details: error.message
        });
    }
});

// Endpoint to change model preference
app.post('/api/model/preference', (req, res) => {
    try {
        const { preference } = req.body;

        if (!['auto', 'online', 'offline'].includes(preference)) {
            return res.status(400).json({
                error: 'Invalid preference',
                valid_options: ['auto', 'online', 'offline']
            });
        }

        const oldPreference = MODEL_PREFERENCE;
        MODEL_PREFERENCE = preference;

        console.log(`Model preference changed from ${oldPreference} to ${preference}`);

        res.json({
            success: true,
            old_preference: oldPreference,
            new_preference: MODEL_PREFERENCE,
            message: `Model preference updated to ${preference}`
        });
    } catch (error) {
        console.error('Error changing model preference:', error);
        res.status(500).json({
            error: 'Failed to change model preference',
            details: error.message
        });
    }
});

// YouTube search endpoint
app.get('/api/youtube/search', async (req, res) => {
    try {
        const { query = 'relaxing music', limit = 15, filter = 'music' } = req.query;

        let searchQuery = query;
        if (filter === 'music' && !query.includes('music')) {
            searchQuery = `${query} music`;
        }

        console.log(`Searching YouTube for: "${searchQuery}" with limit ${limit}`);
        const searchResults = await youtubeSearchApi.GetListByKeyword(searchQuery, false, limit);

        if (searchResults && searchResults.items) {
            console.log(`Found ${searchResults.items.length} YouTube results`);
        }

        res.json(searchResults);
    } catch (error) {
        console.error('Error searching YouTube:', error);
        res.status(500).json({
            error: 'Failed to search YouTube',
            details: error.message
        });
    }
});

// Start server
app.listen(port, () => {
    console.log(`Server running at http://localhost:${port}`);
    console.log(`AI Configuration:`);
    console.log(`  Model Preference: ${MODEL_PREFERENCE}`);
    console.log(`  Online Model: ${OPENROUTER_MODEL} (OpenRouter)`);
    console.log(`  Offline Model: ${LOCAL_MODEL_NAME} (Ollama)`);
    console.log(`  API Endpoints:`);
    console.log(`    - GET /api/model/status - Check model availability`);
    console.log(`    - POST /api/model/preference - Change model preference`);
    console.log(`    - POST /api/stress/predict - Dedicated stress prediction endpoint`);
});
