document.addEventListener('DOMContentLoaded', () => {
    // Get DOM elements for navigation
    const navItems = document.querySelectorAll('.nav-item');
    const profileButton = document.getElementById('profileButton');
    const profileModal = document.getElementById('profileModal');
    const closeModalBtn = document.querySelector('#profileModal .close-modal');
    const saveProfileBtn = document.getElementById('saveProfileBtn');
    const mainNav = document.getElementById('mainNav');
    const navToggleBtn = document.getElementById('navToggleBtn');
    const navPositionBtn = document.getElementById('navPositionBtn');
    const pageContainer = document.querySelector('.page-container');

    // Navigation position state
    let navPosition = localStorage.getItem('navPosition') || 'top';
    let navVisible = true;

    // Initialize nav position
    if (navPosition !== 'top') {
        setNavPosition(navPosition);
    }

    // Toggle navigation visibility
    navToggleBtn.addEventListener('click', () => {
        if (navPosition !== 'top') {
            mainNav.classList.toggle('active');
            navToggleBtn.classList.toggle('active');
            navVisible = !navVisible;
        }
    });

    // Toggle navigation position (top, left, right)
    navPositionBtn.addEventListener('click', () => {
        if (navPosition === 'top') {
            setNavPosition('left');
        } else if (navPosition === 'left') {
            setNavPosition('right');
        } else {
            setNavPosition('top');
        }
    });

    function setNavPosition(position) {
        navPosition = position;
        localStorage.setItem('navPosition', position);

        // Reset classes
        mainNav.classList.remove('sidebar', 'right', 'active');
        pageContainer.classList.remove('nav-sidebar', 'right');
        navToggleBtn.style.display = 'none';
        navVisible = true;

        if (position === 'left') {
            mainNav.classList.add('sidebar');
            pageContainer.classList.add('nav-sidebar');
            pageContainer.style.marginLeft = '240px';
            pageContainer.style.marginTop = '0';
            navToggleBtn.style.display = 'flex';
            navToggleBtn.style.left = '10px';
        } else if (position === 'right') {
            mainNav.classList.add('sidebar', 'right');
            pageContainer.classList.add('nav-sidebar', 'right');
            pageContainer.style.marginRight = '240px';
            pageContainer.style.marginLeft = '0';
            pageContainer.style.marginTop = '0';
            navToggleBtn.style.display = 'flex';
            navToggleBtn.style.left = 'auto';
            navToggleBtn.style.right = '10px';
        } else {
            // Top position
            pageContainer.style.marginLeft = '0';
            pageContainer.style.marginRight = '0';
            pageContainer.style.marginTop = '60px';
        }
    }

    // Original inputs from the stress predictor
    const sleepInput = document.getElementById('sleep');
    const sleepValueDisplay = document.getElementById('sleepValue');
    const homeworkInput = document.getElementById('homework');
    const homeworkValueDisplay = document.getElementById('homeworkValue');
    const examsInput = document.getElementById('exams');
    const socialInput = document.getElementById('social');
    const socialValueDisplay = document.getElementById('socialValue');
    const extracurricularsInput = document.getElementById('extracurriculars');
    const extracurricularsValueDisplay = document.getElementById('extracurricularsValue');
    const nutritionInput = document.getElementById('nutrition');
    const nutritionValueDisplay = document.getElementById('nutritionValue');
    const screenInput = document.getElementById('screen');
    const screenValueDisplay = document.getElementById('screenValue');

    const predictButton = document.getElementById('predictButton');
    const resultArea = document.getElementById('resultArea');
    const loadingIndicator = document.getElementById('loadingIndicator');
    const predictionOutput = document.getElementById('predictionOutput');
    const stressLevelText = document.getElementById('stressLevelText');
    const stressEmoji = document.getElementById('stressEmoji');
    const stressAdviceText = document.getElementById('stressAdviceText');
    const stressMeterFill = document.getElementById('stressMeterFill');
    const personalizedTips = document.getElementById('personalizedTips');

    const saveResultButton = document.getElementById('saveResultButton');
    const historySection = document.getElementById('historySection');
    const historyList = document.getElementById('historyList');
    const clearHistoryButton = document.getElementById('clearHistoryButton');
    const stressChart = document.getElementById('stressChart');

    // New activity page elements
    const playMusicBtn = document.getElementById('playMusicBtn');
    const musicSelect = document.getElementById('musicSelect');
    const volumeControl = document.getElementById('volume');
    const startBreathingBtn = document.getElementById('startBreathingBtn');
    const breathingCircle = document.getElementById('breathingCircle');
    const breathingInstruction = document.getElementById('breathingInstruction');
    const startStretchBtn = document.getElementById('startStretchBtn');
    const prevStretchBtn = document.getElementById('prevStretchBtn');
    const nextStretchBtn = document.getElementById('nextStretchBtn');
    const stretchTime = document.getElementById('stretchTime');
    const stretchInstruction = document.getElementById('stretchInstruction');
    const mindfulnessTimer = document.getElementById('mindfulnessTimer');
    const timeButtons = document.querySelectorAll('.time-btn');
    const startMindfulnessBtn = document.getElementById('startMindfulnessBtn');

    // Chat page elements
    const chatMessages = document.getElementById('chatMessages');
    const userMessageInput = document.getElementById('userMessageInput');
    const sendMessageBtn = document.getElementById('sendMessageBtn');
    const suggestionChips = document.querySelectorAll('.suggestion-chip');

    // Patch: Fix Chat Page display!
    // Instead of trying to query '.page', directly select the three main page containers for reliability
    const mainPage = document.getElementById('mainPage');
    const activitiesPage = document.getElementById('activitiesPage');
    const aiChatPage = document.getElementById('aiChatPage');

    // Load user profile data
    let userProfile = JSON.parse(localStorage.getItem('userProfile')) || {
        name: '',
        age: '',
        grade: '',
        major: '',
        stressors: [],
        avatarUrl: 'https://via.placeholder.com/150'
    };

    // Update UI with profile data
    function updateProfileUI() {
        document.getElementById('profileName').value = userProfile.name;
        document.getElementById('profileAge').value = userProfile.age;
        document.getElementById('profileGrade').value = userProfile.grade;
        document.getElementById('profileMajor').value = userProfile.major;

        // Reset checkboxes
        document.querySelectorAll('input[name="stressor"]').forEach(checkbox => {
            checkbox.checked = false;
        });

        // Check the user's stressors
        userProfile.stressors.forEach(stressor => {
            const checkbox = document.querySelector(`input[name="stressor"][value="${stressor}"]`);
            if (checkbox) checkbox.checked = true;
        });

        // Update avatar
        document.getElementById('profileAvatarPreview').src = userProfile.avatarUrl;
        document.getElementById('profileAvatar').src = userProfile.avatarUrl;

        // Update welcome message
        updateWelcomeMessage();

        // Update greeting text on main page
        updateGreetingText();
    }

    // Update greeting text based on username
    function updateGreetingText() {
        const greetingText = document.getElementById('greetingText');
        const name = userProfile.name || 'you';
        let greeting = '';

        const currentHour = new Date().getHours();
        if (currentHour < 12) {
            greeting = `Good morning, ${name}! How are you feeling today?`;
        } else if (currentHour < 18) {
            greeting = `Good afternoon, ${name}! How are you feeling today?`;
        } else {
            greeting = `Good evening, ${name}! How are you feeling today?`;
        }

        greetingText.textContent = greeting;
    }

    // Welcome message
    const welcomeMessage = document.getElementById('welcomeMessage');

    // Update welcome message based on username
    function updateWelcomeMessage() {
        const name = userProfile.name || 'Student';
        welcomeMessage.innerHTML = `
            <p>Hello, ${name}! Welcome to your advanced AI assistant powered by cutting-edge language models.
            I can help with complex questions, provide detailed explanations, and assist with a wide range of topics.</p>
        `;
    }

    // Initialize stress history data
    let stressHistory = JSON.parse(localStorage.getItem('stressHistory')) || [];
    let stressChartInstance = null;

    // Conversation memory for AI chat
    let conversationHistory = [{
        role: "assistant",
        content: "Hello! I'm your advanced AI assistant powered by state-of-the-art language models. I can help with complex questions, provide detailed explanations, generate code, and assist with a wide range of topics. How can I assist you today?"
    }];
    let lastStressPrediction = null;

    // Chat history management
    let chatHistory = JSON.parse(localStorage.getItem('chatHistory')) || [];
    let currentChatId = null;
    let selectedChatId = null;

    // Helper: Adds message to chat view with text effects
    function addMessage(role, content) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${role}-message`;

        // Add avatar
        const avatarDiv = document.createElement('div');
        avatarDiv.className = 'message-avatar';

        // If user has a profile avatar and it's a user message, use it
        if (role === 'user' && userProfile && userProfile.avatarUrl) {
            avatarDiv.style.backgroundImage = `url('${userProfile.avatarUrl}')`;
        }

        messageDiv.appendChild(avatarDiv);

        // Create message bubble
        const messageBubble = document.createElement('div');
        messageBubble.className = 'message-bubble';

        // Create message content inside bubble
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';

        // Add timestamp
        const timeDiv = document.createElement('div');
        timeDiv.className = 'message-time';
        const now = new Date();
        timeDiv.textContent = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        messageBubble.appendChild(timeDiv);

        // Apply text effects for AI messages
        if (role === 'ai') {
            // Process content for special formatting
            const formattedContent = processContentForEffects(content);

            // For AI messages, we'll add a simple fade-in effect
            messageContent.innerHTML = formattedContent;
            messageContent.style.opacity = '0';
            messageBubble.appendChild(messageContent);
            messageDiv.appendChild(messageBubble);
            chatMessages.appendChild(messageDiv);

            // Fade in the message
            setTimeout(() => {
                // Use a CSS transition for smooth fade-in
                messageContent.style.transition = 'opacity 0.5s ease';
                messageContent.style.opacity = '1';
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }, 100);
        } else {
            // User messages appear instantly
            // Escape HTML for user messages to prevent injection
            const escapedContent = document.createElement('div');
            escapedContent.textContent = content;
            messageContent.innerHTML = `<p>${escapedContent.innerHTML}</p>`;
            messageBubble.appendChild(messageContent);
            messageDiv.appendChild(messageBubble);
            chatMessages.appendChild(messageDiv);
        }

        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    // Process content to add visual effects
    function processContentForEffects(content) {
        // Create a temporary div to safely parse content
        const tempDiv = document.createElement('div');
        tempDiv.textContent = content;
        content = tempDiv.innerHTML;

        // Only apply effects to plain text, not HTML
        // Handle emojis - only match standalone emojis, not digits
        content = content.replace(/(\p{Emoji}+)/gu, (match) => {
            // Skip if it's a digit
            if (/^\d+$/.test(match)) return match;
            return `<span class="large-emoji">${match}</span>`;
        });

        // Highlight important phrases
        const highlightPhrases = [
            'stress', 'anxiety', 'relax', 'breathe', 'sleep', 'exercise',
            'important', 'remember', 'focus', 'break', 'rest', 'study'
        ];

        // Use a more careful approach to avoid replacing inside HTML tags
        highlightPhrases.forEach(phrase => {
            const regex = new RegExp(`\\b${phrase}\\b`, 'gi');
            content = content.replace(regex, `<span class="highlight-text">$&</span>`);
        });

        // Add emphasis to advice sections
        content = content.replace(/(Tip|Advice|Suggestion|Recommendation):/g,
            '<span class="advice-heading">$1:</span>');

        // Format lists with better styling - only at start of line
        content = content.replace(/^(\s*[-*]\s+)(.+)$/gm,
            '<span class="list-item">• $2</span>');

        // Wrap content in paragraph for proper display
        return `<p>${content}</p>`;
    }

    // Helper: initial chat state and welcome
    function initializeChat() {
        chatMessages.innerHTML = '';

        // Get stress level info if available
        let welcomeMessage = "Hello! I'm your advanced AI assistant powered by state-of-the-art language models. I can help with complex questions, provide detailed explanations, generate code, and assist with a wide range of topics. How can I assist you today?";

        // Add personalized welcome if we have user profile
        if (userProfile && userProfile.name) {
            welcomeMessage = `Hello ${userProfile.name}! I'm your advanced AI assistant powered by state-of-the-art language models. I can help with complex questions, provide detailed explanations, generate code, and assist with a wide range of topics. How can I assist you today?`;
        }

        // Add stress level info if available
        if (lastStressPrediction) {
            welcomeMessage += ` I notice your current stress level is ${lastStressPrediction.level} (${lastStressPrediction.score}/100). Would you like some advanced techniques for managing that?`;
        }

        addMessage('ai', welcomeMessage);

        // Initialize model selector
        initializeModelSelector();
    }

    // Initialize model selector functionality
    function initializeModelSelector() {
        const modelPreference = document.getElementById('modelPreference');
        const modelStatus = document.getElementById('modelStatus');
        const aiModelBadge = document.getElementById('aiModelBadge');

        if (!modelPreference || !modelStatus || !aiModelBadge) {
            console.warn('Model selector elements not found');
            return;
        }

        // Check model status on page load
        checkModelStatus();

        // Set up periodic status checking (every 30 seconds)
        setInterval(() => {
            // Only check if we're on the AI chat page
            const aiChatPage = document.getElementById('aiChatPage');
            if (aiChatPage && !aiChatPage.classList.contains('hidden')) {
                checkModelStatus();
            }
        }, 30000);

        // Handle model preference change
        modelPreference.addEventListener('change', async (e) => {
            const newPreference = e.target.value;
            const oldPreference = e.target.dataset.currentValue || 'auto';

            console.log(`Changing model preference from ${oldPreference} to ${newPreference}`);

            try {
                // Show immediate feedback
                const modelStatus = document.getElementById('modelStatus');
                if (modelStatus) {
                    modelStatus.textContent = 'Updating...';
                    modelStatus.className = 'model-status checking';
                }

                const response = await fetch('/api/model/preference', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ preference: newPreference })
                });

                if (response.ok) {
                    const result = await response.json();
                    console.log('Model preference updated:', result);

                    // Store current value for next change
                    e.target.dataset.currentValue = newPreference;

                    // Update status after a short delay to allow server to process
                    setTimeout(() => {
                        checkModelStatus();
                    }, 500);

                    // Show success notification
                    let notificationMessage = '';
                    switch (newPreference) {
                        case 'auto':
                            notificationMessage = 'Auto mode: Will use best available model';
                            break;
                        case 'online':
                            notificationMessage = 'Online mode: Using DeepSeek model';
                            break;
                        case 'offline':
                            notificationMessage = 'Offline mode: Using local Llama model';
                            break;
                    }
                    showNotification(notificationMessage, 'success');
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                console.error('Error updating model preference:', error);

                // Revert the selection
                e.target.value = oldPreference;

                // Show error notification
                showNotification('Failed to update model preference. Please try again.', 'error');

                // Restore status
                checkModelStatus();
            }
        });
    }

    // Check model status
    async function checkModelStatus() {
        const modelStatus = document.getElementById('modelStatus');
        const aiModelBadge = document.getElementById('aiModelBadge');
        const modelPreference = document.getElementById('modelPreference');

        if (!modelStatus || !aiModelBadge || !modelPreference) {
            console.warn('Model status elements not found');
            return;
        }

        try {
            console.log('Checking model status...');
            modelStatus.textContent = 'Checking...';
            modelStatus.className = 'model-status checking';

            const response = await fetch('/api/model/status', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const status = await response.json();
            console.log('Model status received:', status);

            // Update model preference selector
            modelPreference.value = status.current_preference;

            // Update status display based on current preference and availability
            let statusText = '';
            let statusClass = '';
            let badgeText = 'AI';

            const onlineAvailable = status.online_model.status === 'available';
            const offlineAvailable = status.offline_model.status === 'available';

            switch (status.current_preference) {
                case 'auto':
                    if (onlineAvailable) {
                        statusText = 'Auto → Online (DeepSeek)';
                        statusClass = 'online';
                        badgeText = 'DeepSeek';
                    } else if (offlineAvailable) {
                        statusText = 'Auto → Offline (Llama)';
                        statusClass = 'offline';
                        badgeText = 'Llama';
                    } else {
                        statusText = 'No models available';
                        statusClass = 'error';
                        badgeText = 'Error';
                    }
                    break;

                case 'online':
                    if (onlineAvailable) {
                        statusText = 'Online (DeepSeek)';
                        statusClass = 'online';
                        badgeText = 'DeepSeek';
                    } else {
                        statusText = 'Online unavailable';
                        statusClass = 'error';
                        badgeText = 'Error';
                    }
                    break;

                case 'offline':
                    if (offlineAvailable) {
                        statusText = 'Offline (Llama)';
                        statusClass = 'offline';
                        badgeText = 'Llama';
                    } else {
                        statusText = 'Offline unavailable';
                        statusClass = 'error';
                        badgeText = 'Error';
                    }
                    break;

                default:
                    statusText = 'Unknown preference';
                    statusClass = 'error';
                    badgeText = 'Error';
            }

            modelStatus.textContent = statusText;
            modelStatus.className = `model-status ${statusClass}`;
            aiModelBadge.textContent = badgeText;

            console.log(`Status updated: ${statusText} (${statusClass})`);

        } catch (error) {
            console.error('Error checking model status:', error);
            modelStatus.textContent = 'Status check failed';
            modelStatus.className = 'model-status error';
            aiModelBadge.textContent = 'AI';

            // Show user-friendly error
            showNotification('Failed to check model status. Please refresh the page.', 'error');
        }
    }

    // *** Fix navigation logic for Chat Page visibility ***
    navItems.forEach(item => {
        item.addEventListener('click', (e) => {
            e.preventDefault();
            const targetPage = item.getAttribute('data-page');
            // Update active nav item
            navItems.forEach(nav => nav.classList.remove('active'));
            item.classList.add('active');

            // Show selected page, hide others
            // Instead of looping all '.page', explicitly show the right container for better reliability
            if (targetPage === 'main') {
                mainPage.classList.remove('hidden');
                activitiesPage.classList.add('hidden');
                aiChatPage.classList.add('hidden');
            } else if (targetPage === 'activities') {
                mainPage.classList.add('hidden');
                activitiesPage.classList.remove('hidden');
                aiChatPage.classList.add('hidden');
            } else if (targetPage === 'ai-chat') {
                mainPage.classList.add('hidden');
                activitiesPage.classList.add('hidden');
                aiChatPage.classList.remove('hidden');
                initializeChat();
            }
            // Close mobile nav if open
            if (navPosition !== 'top' && !navVisible) {
                mainNav.classList.remove('active');
                navToggleBtn.classList.remove('active');
                navVisible = true;
            }
        });
    });

    // AI Response handler
    async function getAIChatResponse(message) {
        // Use user's name if set
        const profileName = (userProfile && userProfile.name) ? userProfile.name : 'student';
        // Compose a helpful system prompt
        let basePrompt = "You are a kind, concise, and positive AI that helps students with stress, studying, and wellness.";
        if (lastStressPrediction) {
            basePrompt += ` The user recently ran a stress prediction. Their stress level was "${lastStressPrediction.level}", score: ${lastStressPrediction.score}, with advice: "${lastStressPrediction.advice}". Factor this recent result into your responses if relevant.`;
        }
        basePrompt += " If the user's input mentions stress, context, their name, or study life, reply with specific supportive suggestions.";
        basePrompt += " Keep responses short, friendly, actionable, and supportive.";
        // Add their name to personalize
        basePrompt += ` Address the user as "${profileName}".`;

        conversationHistory.push({
            role: "user",
            content: message
        });
        const history = conversationHistory.slice(-10);

        // Show thinking / typing indicator with avatar
        const thinkingDiv = document.createElement('div');
        thinkingDiv.className = 'message ai-message thinking';

        // Add avatar
        const avatarDiv = document.createElement('div');
        avatarDiv.className = 'message-avatar';
        thinkingDiv.appendChild(avatarDiv);

        // Add message bubble with typing animation
        const messageBubble = document.createElement('div');
        messageBubble.className = 'message-bubble';

        // Add typing indicator
        const typingIndicator = document.createElement('div');
        typingIndicator.className = 'typing-indicator';
        typingIndicator.innerHTML = `
            <span></span>
            <span></span>
            <span></span>
        `;

        messageBubble.appendChild(typingIndicator);
        thinkingDiv.appendChild(messageBubble);
        chatMessages.appendChild(thinkingDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;

        try {
            // Get current model preference
            const modelPreference = document.getElementById('modelPreference');
            const currentPreference = modelPreference ? modelPreference.value : 'auto';

            // Make request to our server endpoint
            const response = await fetch('/api/chat/completions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    messages: [
                        { role: "system", content: basePrompt },
                        ...history
                    ],
                    stressData: lastStressPrediction,
                    modelPreference: currentPreference
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const completion = await response.json();

            // Add response to conversation and display
            conversationHistory.push({
                role: "assistant",
                content: completion.content
            });
            thinkingDiv.remove();

            // Add model info to the message if available
            let messageContent = completion.content;
            if (completion.model_used) {
                const modelInfo = completion.model_used === 'online' ? '🌐 DeepSeek' : '💻 Llama';
                console.log(`Response from ${modelInfo} (${completion.model_name})`);
            }

            addMessage('ai', messageContent);

            // Auto-save chat if it has a current ID
            if (currentChatId) {
                // Find the chat in history
                const chatIndex = chatHistory.findIndex(chat => chat.id === currentChatId);
                if (chatIndex !== -1) {
                    // Update the chat
                    chatHistory[chatIndex].messages = [...conversationHistory];
                    chatHistory[chatIndex].messageCount = conversationHistory.length;

                    // Save to localStorage
                    localStorage.setItem('chatHistory', JSON.stringify(chatHistory));
                }
            }
        } catch (e) {
            console.error('Chat error:', e);
            thinkingDiv.remove();

            let errorMessage = "Sorry, I couldn't process your request right now. Please try again.";

            // Provide more specific error messages
            if (e.message.includes('503')) {
                errorMessage = "AI service is temporarily unavailable. Please check your internet connection or try switching to offline mode.";
            } else if (e.message.includes('timeout')) {
                errorMessage = "Request timed out. Please try again.";
            }

            addMessage('ai', errorMessage);
        }
    }

    // Chat send on button click
    sendMessageBtn.addEventListener('click', () => {
        const message = userMessageInput.value.trim();
        if (!message) return;
        addMessage('user', message);
        getAIChatResponse(message);
        userMessageInput.value = '';
    });

    // Chat send on enter keypress in textarea
    userMessageInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            const message = userMessageInput.value.trim();
            if (!message) return;
            addMessage('user', message);
            getAIChatResponse(message);
            userMessageInput.value = '';
        }
    });

    // Suggestion buttons for chat
    suggestionChips.forEach(chip => {
        chip.addEventListener('click', () => {
            const message = chip.getAttribute('data-message');
            userMessageInput.value = message;
            userMessageInput.focus();
        });
    });

    // Chat history modal elements
    const chatHistoryModal = document.getElementById('chatHistoryModal');
    const closeChatHistoryBtn = document.getElementById('closeChatHistoryBtn');
    const chatHistoryList = document.getElementById('chatHistoryList');
    const chatHistoryPreview = document.getElementById('chatHistoryPreview');
    const chatHistorySearch = document.getElementById('chatHistorySearch');
    const searchChatHistoryBtn = document.getElementById('searchChatHistoryBtn');
    const loadSelectedChatBtn = document.getElementById('loadSelectedChatBtn');
    const deleteSelectedChatBtn = document.getElementById('deleteSelectedChatBtn');
    const exportSelectedChatBtn = document.getElementById('exportSelectedChatBtn');
    const deleteAllChatsBtn = document.getElementById('deleteAllChatsBtn');

    // Chat action buttons
    const saveChatBtn = document.getElementById('saveChatBtn');
    const historyBtn = document.getElementById('historyBtn');
    const exportChatBtn = document.getElementById('exportChatBtn');
    const clearChatBtn = document.getElementById('clearChatBtn');
    const chatSaveStatus = document.getElementById('chatSaveStatus');

    // Save current chat
    saveChatBtn.addEventListener('click', () => {
        if (conversationHistory.length <= 1) {
            showSaveStatus('No conversation to save', false);
            return;
        }

        // Create a title from the first user message
        const firstUserMessage = conversationHistory.find(msg => msg.role === 'user');
        let chatTitle = firstUserMessage ? firstUserMessage.content : 'New Chat';

        // Truncate title if too long
        if (chatTitle.length > 50) {
            chatTitle = chatTitle.substring(0, 47) + '...';
        }

        // Create a new chat object
        const newChat = {
            id: currentChatId || generateChatId(),
            title: chatTitle,
            date: new Date().toISOString(),
            messages: [...conversationHistory],
            messageCount: conversationHistory.length
        };

        // Update or add to chat history
        if (currentChatId) {
            // Update existing chat
            const index = chatHistory.findIndex(chat => chat.id === currentChatId);
            if (index !== -1) {
                chatHistory[index] = newChat;
            } else {
                chatHistory.push(newChat);
            }
        } else {
            // Add new chat
            chatHistory.push(newChat);
            currentChatId = newChat.id;
        }

        // Save to localStorage
        localStorage.setItem('chatHistory', JSON.stringify(chatHistory));

        // Show save confirmation
        showSaveStatus('Chat saved successfully!', true);
    });

    // Show chat history modal
    historyBtn.addEventListener('click', () => {
        updateChatHistoryList();
        chatHistoryModal.classList.add('show');
    });

    // Export current chat
    exportChatBtn.addEventListener('click', () => {
        if (conversationHistory.length <= 1) {
            showSaveStatus('No conversation to export', false);
            return;
        }

        exportChat(conversationHistory);
    });

    // Clear current chat
    clearChatBtn.addEventListener('click', () => {
        if (confirm('Are you sure you want to clear the current chat?')) {
            // Reset conversation history to initial state
            conversationHistory = [{
                role: "assistant",
                content: "Hello! I'm your advanced AI assistant powered by state-of-the-art language models. I can help with complex questions, provide detailed explanations, generate code, and assist with a wide range of topics. How can I assist you today?"
            }];

            // Reset current chat ID
            currentChatId = null;

            // Clear chat messages display
            initializeChat();

            showSaveStatus('Chat cleared', true);
        }
    });

    // Close chat history modal
    closeChatHistoryBtn.addEventListener('click', () => {
        chatHistoryModal.classList.remove('show');
    });

    // Search chat history
    searchChatHistoryBtn.addEventListener('click', () => {
        const searchTerm = chatHistorySearch.value.trim().toLowerCase();
        if (!searchTerm) {
            updateChatHistoryList();
            return;
        }

        // Filter chats by search term
        const filteredChats = chatHistory.filter(chat => {
            // Search in title
            if (chat.title.toLowerCase().includes(searchTerm)) {
                return true;
            }

            // Search in messages
            return chat.messages.some(msg =>
                msg.content.toLowerCase().includes(searchTerm)
            );
        });

        updateChatHistoryList(filteredChats);
    });

    // Load selected chat
    loadSelectedChatBtn.addEventListener('click', () => {
        if (!selectedChatId) return;

        const selectedChat = chatHistory.find(chat => chat.id === selectedChatId);
        if (selectedChat) {
            // Load conversation history
            conversationHistory = [...selectedChat.messages];
            currentChatId = selectedChat.id;

            // Update chat display
            chatMessages.innerHTML = '';
            conversationHistory.forEach(msg => {
                if (msg.role !== 'system') {
                    addMessage(msg.role, msg.content);
                }
            });

            // Close modal
            chatHistoryModal.classList.remove('show');

            showSaveStatus('Chat loaded successfully', true);
        }
    });

    // Delete selected chat
    deleteSelectedChatBtn.addEventListener('click', () => {
        if (!selectedChatId) return;

        if (confirm('Are you sure you want to delete this chat?')) {
            // Remove from chat history
            chatHistory = chatHistory.filter(chat => chat.id !== selectedChatId);

            // Save to localStorage
            localStorage.setItem('chatHistory', JSON.stringify(chatHistory));

            // Reset selected chat
            selectedChatId = null;

            // Update chat history list
            updateChatHistoryList();

            // Disable buttons
            loadSelectedChatBtn.disabled = true;
            deleteSelectedChatBtn.disabled = true;
            exportSelectedChatBtn.disabled = true;

            // Clear preview
            chatHistoryPreview.innerHTML = `
                <div class="preview-placeholder">
                    <p>Select a chat to preview</p>
                </div>
            `;
        }
    });

    // Export selected chat
    exportSelectedChatBtn.addEventListener('click', () => {
        if (!selectedChatId) return;

        const selectedChat = chatHistory.find(chat => chat.id === selectedChatId);
        if (selectedChat) {
            exportChat(selectedChat.messages, selectedChat.title);
        }
    });

    // Delete all chats
    deleteAllChatsBtn.addEventListener('click', () => {
        if (chatHistory.length === 0) return;

        if (confirm('Are you sure you want to delete ALL saved chats? This cannot be undone.')) {
            // Clear chat history
            chatHistory = [];

            // Save to localStorage
            localStorage.setItem('chatHistory', JSON.stringify(chatHistory));

            // Reset selected chat
            selectedChatId = null;

            // Update chat history list
            updateChatHistoryList();

            // Disable buttons
            loadSelectedChatBtn.disabled = true;
            deleteSelectedChatBtn.disabled = true;
            exportSelectedChatBtn.disabled = true;

            // Clear preview
            chatHistoryPreview.innerHTML = `
                <div class="preview-placeholder">
                    <p>Select a chat to preview</p>
                </div>
            `;
        }
    });

    // Update chat history list
    function updateChatHistoryList(chats = chatHistory) {
        chatHistoryList.innerHTML = '';

        if (chats.length === 0) {
            chatHistoryList.innerHTML = `<div class="empty-history-message">No saved chats yet</div>`;
            return;
        }

        // Sort chats by date (newest first)
        const sortedChats = [...chats].sort((a, b) =>
            new Date(b.date) - new Date(a.date)
        );

        sortedChats.forEach(chat => {
            const chatItem = document.createElement('div');
            chatItem.className = 'chat-history-item';
            chatItem.dataset.chatId = chat.id;

            if (chat.id === selectedChatId) {
                chatItem.classList.add('selected');
            }

            // Format date
            const chatDate = new Date(chat.date);
            const formattedDate = chatDate.toLocaleDateString() + ' ' +
                                 chatDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

            // Get first user message for preview
            const firstUserMsg = chat.messages.find(msg => msg.role === 'user');
            const previewText = firstUserMsg ? firstUserMsg.content : 'No messages';

            chatItem.innerHTML = `
                <div class="chat-history-item-header">
                    <div class="chat-history-item-title">${chat.title}</div>
                    <div class="chat-history-item-date">${formattedDate}</div>
                </div>
                <div class="chat-history-item-preview">${previewText}</div>
                <div class="chat-history-item-meta">
                    <span>${chat.messageCount} messages</span>
                </div>
            `;

            chatItem.addEventListener('click', () => {
                // Deselect all items
                document.querySelectorAll('.chat-history-item').forEach(item => {
                    item.classList.remove('selected');
                });

                // Select this item
                chatItem.classList.add('selected');
                selectedChatId = chat.id;

                // Enable buttons
                loadSelectedChatBtn.disabled = false;
                deleteSelectedChatBtn.disabled = false;
                exportSelectedChatBtn.disabled = false;

                // Show preview
                showChatPreview(chat);
            });

            chatHistoryList.appendChild(chatItem);
        });
    }

    // Show chat preview
    function showChatPreview(chat) {
        chatHistoryPreview.innerHTML = '';

        // Show only a subset of messages for preview (max 10)
        const previewMessages = chat.messages.slice(0, 10);

        previewMessages.forEach(msg => {
            if (msg.role === 'system') return;

            const messageDiv = document.createElement('div');
            messageDiv.className = `preview-message ${msg.role}`;

            const messageHeader = document.createElement('div');
            messageHeader.className = 'preview-message-header';
            messageHeader.textContent = msg.role === 'assistant' ? 'AI' : 'You';

            const messageContent = document.createElement('div');
            messageContent.className = 'preview-message-content';
            messageContent.textContent = msg.content;

            messageDiv.appendChild(messageHeader);
            messageDiv.appendChild(messageContent);
            chatHistoryPreview.appendChild(messageDiv);
        });

        // Add message count indicator if there are more messages
        if (chat.messages.length > 10) {
            const moreMessages = document.createElement('div');
            moreMessages.className = 'preview-more-messages';
            moreMessages.textContent = `+ ${chat.messages.length - 10} more messages`;
            chatHistoryPreview.appendChild(moreMessages);
        }
    }

    // Generate a unique ID for chats
    function generateChatId() {
        return Date.now().toString(36) + Math.random().toString(36).substring(2);
    }

    // Show save status message
    function showSaveStatus(message, isSuccess) {
        chatSaveStatus.textContent = message;
        chatSaveStatus.style.color = isSuccess ? '#2ecc71' : '#e74c3c';
        chatSaveStatus.classList.add('show');

        setTimeout(() => {
            chatSaveStatus.classList.remove('show');
        }, 3000);
    }

    // Show notification message
    function showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;

        // Style the notification
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 10000;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
            max-width: 300px;
            word-wrap: break-word;
        `;

        // Set background color based on type
        switch (type) {
            case 'success':
                notification.style.backgroundColor = '#2ecc71';
                break;
            case 'error':
                notification.style.backgroundColor = '#e74c3c';
                break;
            case 'warning':
                notification.style.backgroundColor = '#f39c12';
                break;
            default:
                notification.style.backgroundColor = '#3498db';
        }

        // Add to page
        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        }, 100);

        // Remove after delay
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    // Export chat as JSON file
    function exportChat(messages, title = 'chat-export') {
        // Create export object
        const exportData = {
            title: title,
            date: new Date().toISOString(),
            messages: messages
        };

        // Convert to JSON
        const jsonData = JSON.stringify(exportData, null, 2);

        // Create blob and download link
        const blob = new Blob([jsonData], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        // Create download link
        const a = document.createElement('a');
        a.href = url;
        a.download = `${title.replace(/[^a-z0-9]/gi, '-').toLowerCase()}-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();

        // Clean up
        setTimeout(() => {
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }, 100);
    }

    // Add stress level suggestion chip if not present
    const suggestionsDiv = document.querySelector('.suggestion-chips');
    if (!document.querySelector('.suggestion-chip[data-message="Help me with my current stress level"]')) {
        const stressChip = document.createElement('button');
        stressChip.className = 'suggestion-chip';
        stressChip.setAttribute('data-message', 'Help me with my current stress level');
        stressChip.textContent = 'My stress level';
        stressChip.addEventListener('click', () => {
            userMessageInput.value = 'Help me with my current stress level';
            userMessageInput.focus();
        });
        suggestionsDiv.appendChild(stressChip);
    }

    // AI Music Player - with improved initialization
    let aiAudioElement = null;
    let isAiPlaying = false;
    let aiProgressInterval = null;
    let aiCurrentTime = 0;
    let aiTotalDuration = 0;
    let lastVolume = 0.7; // Default volume (70%)

    // AI Music elements
    const aiMusicMood = document.getElementById('aiMusicMood');
    const aiMusicStyle = document.getElementById('aiMusicStyle');
    const aiMusicDuration = document.getElementById('aiMusicDuration');
    const aiMusicSource = document.getElementById('aiMusicSource');
    const generateAiMusicBtn = document.getElementById('generateAiMusicBtn');
    const aiGenerating = document.querySelector('.ai-generating');
    const aiTrackPlayer = document.querySelector('.ai-track-player');
    const aiTrackName = document.getElementById('aiTrackName');
    const aiTrackDescription = document.getElementById('aiTrackDescription');
    const playAiMusicBtn = document.getElementById('playAiMusicBtn');
    const aiMusicProgressFill = document.getElementById('aiMusicProgressFill');
    const aiMusicTime = document.getElementById('aiMusicTime');
    const aiVolume = document.getElementById('aiVolume');

    // AI Music tracks (mapped by mood and style) with multiple format options
    const aiMusicTracks = {
        // Calm tracks
        calm_ambient: {
            mp3: 'https://soundbible.com/mp3/Summer_Meadow-GlorySunz-1691337606.mp3',
            ogg: 'https://soundbible.com/grab.php?id=1661&type=ogg',
            wav: 'https://soundbible.com/grab.php?id=1661&type=wav',
            fallback: 'https://cdn.pixabay.com/download/audio/2022/03/15/audio_c8e0386e38.mp3?filename=ambient-piano-amp-strings-10711.mp3'
        },
        calm_lofi: {
            mp3: 'https://soundbible.com/mp3/Soft_Piano_Music-Mark_Bodino-1638321243.mp3',
            ogg: 'https://soundbible.com/grab.php?id=1638&type=ogg',
            wav: 'https://soundbible.com/grab.php?id=1638&type=wav',
            fallback: 'https://cdn.pixabay.com/download/audio/2022/01/18/audio_d0c6ff1bbd.mp3?filename=lofi-study-112191.mp3'
        },
        calm_classical: {
            mp3: 'https://soundbible.com/mp3/wind-chimes-sound-20-8-bit-11025hz-mono-22secs.mp3',
            ogg: 'https://soundbible.com/grab.php?id=2124&type=ogg',
            wav: 'https://soundbible.com/grab.php?id=2124&type=wav',
            fallback: 'https://cdn.pixabay.com/download/audio/2022/04/27/audio_c3b45a7578.mp3?filename=relaxing-mountains-rivers-streams-running-water-18178.mp3'
        },
        calm_nature: {
            mp3: 'https://soundbible.com/mp3/Summer_Meadow-GlorySunz-1691337606.mp3',
            ogg: 'https://soundbible.com/grab.php?id=1661&type=ogg',
            wav: 'https://soundbible.com/grab.php?id=1661&type=wav',
            fallback: 'https://cdn.pixabay.com/download/audio/2021/09/06/audio_8a901c8cf1.mp3?filename=birds-in-spring-116211.mp3'
        },

        // Focus tracks
        focus_ambient: {
            mp3: 'https://soundbible.com/mp3/Soft_Piano_Music-Mark_Bodino-1638321243.mp3',
            ogg: 'https://soundbible.com/grab.php?id=1638&type=ogg',
            wav: 'https://soundbible.com/grab.php?id=1638&type=wav',
            fallback: 'https://cdn.pixabay.com/download/audio/2022/03/10/audio_270f8b7d1f.mp3?filename=cinematic-documentary-piano-10709.mp3'
        },
        focus_lofi: {
            mp3: 'https://soundbible.com/mp3/Soft_Piano_Music-Mark_Bodino-1638321243.mp3',
            ogg: 'https://soundbible.com/grab.php?id=1638&type=ogg',
            wav: 'https://soundbible.com/grab.php?id=1638&type=wav',
            fallback: 'https://cdn.pixabay.com/download/audio/2022/05/16/audio_1333dfb36b.mp3?filename=lofi-chill-14093.mp3'
        },
        focus_classical: {
            mp3: 'https://soundbible.com/mp3/wind-chimes-sound-20-8-bit-11025hz-mono-22secs.mp3',
            ogg: 'https://soundbible.com/grab.php?id=2124&type=ogg',
            wav: 'https://soundbible.com/grab.php?id=2124&type=wav',
            fallback: 'https://cdn.pixabay.com/download/audio/2022/03/09/audio_c8b0e12cdf.mp3?filename=inspiring-cinematic-ambient-116199.mp3'
        },
        focus_nature: {
            mp3: 'https://soundbible.com/mp3/Summer_Meadow-GlorySunz-1691337606.mp3',
            ogg: 'https://soundbible.com/grab.php?id=1661&type=ogg',
            wav: 'https://soundbible.com/grab.php?id=1661&type=wav',
            fallback: 'https://cdn.pixabay.com/download/audio/2021/09/06/audio_00f0bd3211.mp3?filename=forest-with-small-river-birds-and-nature-field-recording-6735.mp3'
        },

        // Uplifting tracks
        uplifting_ambient: {
            mp3: 'https://soundbible.com/mp3/Soft_Piano_Music-Mark_Bodino-1638321243.mp3',
            ogg: 'https://soundbible.com/grab.php?id=1638&type=ogg',
            wav: 'https://soundbible.com/grab.php?id=1638&type=wav',
            fallback: 'https://cdn.pixabay.com/download/audio/2022/01/20/audio_d0ce7e5fb1.mp3?filename=uplifting-corporate-112872.mp3'
        },
        uplifting_lofi: {
            mp3: 'https://soundbible.com/mp3/Soft_Piano_Music-Mark_Bodino-1638321243.mp3',
            ogg: 'https://soundbible.com/grab.php?id=1638&type=ogg',
            wav: 'https://soundbible.com/grab.php?id=1638&type=wav',
            fallback: 'https://cdn.pixabay.com/download/audio/2022/05/27/audio_1808fbf07a.mp3?filename=lofi-study-beat-14092.mp3'
        },
        uplifting_classical: {
            mp3: 'https://soundbible.com/mp3/wind-chimes-sound-20-8-bit-11025hz-mono-22secs.mp3',
            ogg: 'https://soundbible.com/grab.php?id=2124&type=ogg',
            wav: 'https://soundbible.com/grab.php?id=2124&type=wav',
            fallback: 'https://cdn.pixabay.com/download/audio/2022/04/20/audio_0f1f2d8d1a.mp3?filename=inspiring-cinematic-ambient-116199.mp3'
        },
        uplifting_nature: {
            mp3: 'https://soundbible.com/mp3/Summer_Meadow-GlorySunz-1691337606.mp3',
            ogg: 'https://soundbible.com/grab.php?id=1661&type=ogg',
            wav: 'https://soundbible.com/grab.php?id=1661&type=wav',
            fallback: 'https://cdn.pixabay.com/download/audio/2021/09/06/audio_8a901c8cf1.mp3?filename=birds-in-spring-116211.mp3'
        },

        // Sleep tracks
        sleep_ambient: {
            mp3: 'https://soundbible.com/mp3/Zen_Temple_Bell-SoundBible.com-331362457.mp3',
            ogg: 'https://soundbible.com/grab.php?id=2026&type=ogg',
            wav: 'https://soundbible.com/grab.php?id=2026&type=wav',
            fallback: 'https://cdn.pixabay.com/download/audio/2022/01/18/audio_d0c6ff1bbd.mp3?filename=lofi-study-112191.mp3'
        },
        sleep_lofi: {
            mp3: 'https://soundbible.com/mp3/Soft_Piano_Music-Mark_Bodino-1638321243.mp3',
            ogg: 'https://soundbible.com/grab.php?id=1638&type=ogg',
            wav: 'https://soundbible.com/grab.php?id=1638&type=wav',
            fallback: 'https://cdn.pixabay.com/download/audio/2022/05/27/audio_1808fbf07a.mp3?filename=lofi-study-beat-14092.mp3'
        },
        sleep_classical: {
            mp3: 'https://soundbible.com/mp3/wind-chimes-sound-20-8-bit-11025hz-mono-22secs.mp3',
            ogg: 'https://soundbible.com/grab.php?id=2124&type=ogg',
            wav: 'https://soundbible.com/grab.php?id=2124&type=wav',
            fallback: 'https://cdn.pixabay.com/download/audio/2022/03/15/audio_c8e0386e38.mp3?filename=ambient-piano-amp-strings-10711.mp3'
        },
        sleep_nature: {
            mp3: 'https://soundbible.com/mp3/Summer_Meadow-GlorySunz-1691337606.mp3',
            ogg: 'https://soundbible.com/grab.php?id=1661&type=ogg',
            wav: 'https://soundbible.com/grab.php?id=1661&type=wav',
            fallback: 'https://cdn.pixabay.com/download/audio/2021/09/06/audio_00f0bd3211.mp3?filename=forest-with-small-river-birds-and-nature-field-recording-6735.mp3'
        }
    };

    // AI Music Generation
    generateAiMusicBtn.addEventListener('click', () => {
        // Show generating animation
        aiGenerating.classList.remove('hidden');
        aiTrackPlayer.classList.add('hidden');

        // Get user preferences
        const mood = aiMusicMood.value;
        const style = aiMusicStyle.value;
        const duration = aiMusicDuration.value;
        const source = aiMusicSource.value;

        // Handle different music sources
        if (source === 'youtube') {
            // YouTube Search option
            // Get search query from input or generate one based on mood/style
            let searchQuery = youtubeSearchInput.value.trim();
            if (!searchQuery) {
                // Generate a more specific search query based on mood and style
                const moodTerms = {
                    calm: ['relaxing', 'peaceful', 'calming'],
                    focus: ['concentration', 'focus', 'study'],
                    uplifting: ['uplifting', 'motivational', 'positive'],
                    sleep: ['sleep', 'bedtime', 'lullaby']
                };

                const styleTerms = {
                    ambient: ['ambient', 'atmospheric', 'background'],
                    lofi: ['lofi', 'lo-fi', 'chillhop'],
                    classical: ['classical', 'piano', 'orchestral'],
                    nature: ['nature sounds', 'birds', 'rain']
                };

                // Get random terms for more variety
                const moodTerm = moodTerms[mood][Math.floor(Math.random() * moodTerms[mood].length)];
                const styleTerm = styleTerms[style][Math.floor(Math.random() * styleTerms[style].length)];

                searchQuery = `${moodTerm} ${styleTerm} music`;
                youtubeSearchInput.value = searchQuery;
            }

            console.log('Searching YouTube for:', searchQuery);

            // Show generating animation while fetching
            setTimeout(async () => {
                try {
                    // First attempt with the primary search query
                    let response = await fetch(`/api/youtube/search?query=${encodeURIComponent(searchQuery)}&limit=15`);
                    if (!response.ok) throw new Error('Failed to search YouTube');

                    let data = await response.json();

                    // If no results, try a more generic search
                    if (!data.items || data.items.length === 0) {
                        console.log('No results found, trying more generic search');
                        const genericQuery = `${mood} music`;
                        response = await fetch(`/api/youtube/search?query=${encodeURIComponent(genericQuery)}&limit=15`);
                        if (!response.ok) throw new Error('Failed to search YouTube with generic query');

                        data = await response.json();
                    }

                    if (data.items && data.items.length > 0) {
                        // Try to find a suitable video (not too short, not too long)
                        let selectedVideoIndex = 0;

                        // Try to find a video with a reasonable duration if that info is available
                        for (let i = 0; i < Math.min(5, data.items.length); i++) {
                            const item = data.items[i];
                            // If we have duration info, prefer videos between 3 and 15 minutes
                            if (item.lengthSeconds) {
                                const duration = parseInt(item.lengthSeconds);
                                if (duration >= 180 && duration <= 900) {
                                    selectedVideoIndex = i;
                                    break;
                                }
                            }
                        }

                        // Get the selected video ID
                        const videoId = data.items[selectedVideoIndex].id;
                        console.log('Selected YouTube video ID:', videoId);

                        try {
                            // Get video details
                            const videoInfoResponse = await fetch(`/api/youtube/video-info/${videoId}`);
                            if (!videoInfoResponse.ok) throw new Error('Failed to get video info');

                            const videoInfo = await videoInfoResponse.json();

                            // Generate YouTube track
                            generateYouTubeTrack(videoInfo, mood, style);
                        } catch (videoInfoError) {
                            console.error('Error getting video info:', videoInfoError);

                            // Try the next video in the results
                            if (data.items.length > selectedVideoIndex + 1) {
                                const nextVideoId = data.items[selectedVideoIndex + 1].id;
                                console.log('Trying next video:', nextVideoId);

                                const nextVideoInfoResponse = await fetch(`/api/youtube/video-info/${nextVideoId}`);
                                if (!nextVideoInfoResponse.ok) throw new Error('Failed to get next video info');

                                const nextVideoInfo = await nextVideoInfoResponse.json();
                                generateYouTubeTrack(nextVideoInfo, mood, style);
                            } else {
                                throw new Error('Failed to get video info for any result');
                            }
                        }
                    } else {
                        throw new Error('No YouTube results found');
                    }
                } catch (error) {
                    console.error('YouTube search error:', error);

                    // Provide specific error messages
                    let errorTitle = 'Error Finding YouTube Track';
                    let errorDescription = 'Falling back to AI generated music.';

                    if (error.message.includes('Could not extract functions')) {
                        errorTitle = 'YouTube Search Temporarily Unavailable';
                        errorDescription = 'YouTube search is temporarily unavailable. Using relaxation music instead.';
                    }

                    // Show error message
                    aiTrackName.textContent = errorTitle;
                    aiTrackDescription.textContent = errorDescription;

                    // Fall back to AI music with a slight delay
                    setTimeout(async () => {
                        await generateAiTrack(mood, style, duration);
                    }, 1000);
                } finally {
                    // Hide generating animation, show player
                    aiGenerating.classList.add('hidden');
                    aiTrackPlayer.classList.remove('hidden');
                }
            }, 1500);
        } else if (source === 'youtube-url') {
            // YouTube URL option
            const youtubeUrl = youtubeUrlInput.value.trim();

            if (!youtubeUrl) {
                alert('Please enter a YouTube URL or video ID');
                aiGenerating.classList.add('hidden');
                return;
            }

            console.log('Processing YouTube URL:', youtubeUrl);

            // Show generating animation while processing
            setTimeout(async () => {
                try {
                    // First, validate the YouTube URL
                    const validateResponse = await fetch(`/api/youtube/validate?url=${encodeURIComponent(youtubeUrl)}`);
                    if (!validateResponse.ok) throw new Error('Failed to validate YouTube URL');

                    const validateData = await validateResponse.json();

                    if (!validateData.valid) {
                        throw new Error(validateData.error || 'Invalid YouTube URL or video');
                    }

                    // Use the validated video ID
                    const videoId = validateData.videoId;
                    console.log('Validated YouTube video ID:', videoId, 'Title:', validateData.title);

                    // Get detailed video info
                    const videoInfoResponse = await fetch(`/api/youtube/video-info/${videoId}`);
                    if (!videoInfoResponse.ok) throw new Error('Failed to get video info');

                    const videoInfo = await videoInfoResponse.json();

                    // Generate YouTube track
                    generateYouTubeTrack(videoInfo, mood, style);
                } catch (error) {
                    console.error('YouTube URL processing error:', error);

                    // Provide specific error messages based on error type
                    let errorTitle = 'Error Processing YouTube URL';
                    let errorDescription = 'Please check the URL and try again, or use AI generated music.';

                    if (error.message.includes('Could not extract functions')) {
                        errorTitle = 'YouTube Temporarily Unavailable';
                        errorDescription = 'YouTube audio extraction is temporarily unavailable. Using relaxation music instead.';
                    } else if (error.message.includes('Video unavailable')) {
                        errorTitle = 'Video Not Available';
                        errorDescription = 'This video is not available. Using relaxation music instead.';
                    } else if (error.message.includes('private')) {
                        errorTitle = 'Private Video';
                        errorDescription = 'This video is private and cannot be played. Using relaxation music instead.';
                    } else if (error.message.includes('timeout')) {
                        errorTitle = 'Request Timed Out';
                        errorDescription = 'The request took too long. Using relaxation music instead.';
                    }

                    // Show error message
                    aiTrackName.textContent = errorTitle;
                    aiTrackDescription.textContent = errorDescription;

                    // Fall back to AI music with a slight delay
                    setTimeout(async () => {
                        await generateAiTrack(mood, style, duration);
                    }, 1000);
                } finally {
                    // Hide generating animation, show player
                    aiGenerating.classList.add('hidden');
                    aiTrackPlayer.classList.remove('hidden');
                }
            }, 1500);
        } else {
            // Simulate AI generation time for predefined tracks
            setTimeout(async () => {
                try {
                    // Generate AI music track
                    await generateAiTrack(mood, style, duration);
                } catch (error) {
                    console.error('Error generating AI track:', error);

                    // Show error message
                    aiTrackName.textContent = 'Error Generating Music';
                    aiTrackDescription.textContent = 'Please try again or check your audio settings.';
                } finally {
                    // Always hide generating animation and show player
                    aiGenerating.classList.add('hidden');
                    aiTrackPlayer.classList.remove('hidden');
                }
            }, 2500); // Simulate 2.5 seconds of "AI generation"
        }
    });

    // Play AI Music
    playAiMusicBtn.addEventListener('click', () => {
        if (isAiPlaying) {
            stopAiMusic();
        } else {
            playAiMusic();
        }
    });

    // AI Volume control - improved with immediate feedback and state persistence
    aiVolume.addEventListener('input', () => {
        const newVolume = aiVolume.value / 100;

        // Store the volume value for future use
        lastVolume = newVolume;

        // Apply to current audio element if it exists
        if (aiAudioElement) {
            aiAudioElement.volume = newVolume;
            console.log(`Volume changed to: ${newVolume * 100}%`);
        } else {
            console.log(`Volume set to: ${newVolume * 100}% (will apply when audio is created)`);
        }

        // Visual feedback for volume change
        const volumePercentage = Math.round(newVolume * 100);
        if (volumePercentage === 0) {
            // Show muted icon or indication
            aiVolume.classList.add('muted');
        } else {
            aiVolume.classList.remove('muted');
        }
    });

    // Also handle change event for when slider is released
    aiVolume.addEventListener('change', () => {
        const newVolume = aiVolume.value / 100;
        lastVolume = newVolume;

        if (aiAudioElement) {
            aiAudioElement.volume = newVolume;
            console.log(`Volume set on change: ${newVolume * 100}%`);
        }
    });

    // Initialize volume slider to default or stored value
    window.addEventListener('DOMContentLoaded', () => {
        // Set initial volume (70% is a good default)
        if (aiVolume) {
            aiVolume.value = lastVolume * 100;
        }
    });

    // YouTube input options
    const youtubeSearchGroup = document.querySelector('.youtube-search-group');
    const youtubeUrlGroup = document.querySelector('.youtube-url-group');
    const youtubeSearchInput = document.getElementById('youtubeSearchInput');
    const youtubeUrlInput = document.getElementById('youtubeUrlInput');

    // Show/hide YouTube input options based on source selection
    aiMusicSource.addEventListener('change', () => {
        // Hide all YouTube input groups first
        youtubeSearchGroup.style.display = 'none';
        youtubeUrlGroup.style.display = 'none';

        // Show the appropriate group based on selection
        if (aiMusicSource.value === 'youtube') {
            youtubeSearchGroup.style.display = 'block';
        } else if (aiMusicSource.value === 'youtube-url') {
            youtubeUrlGroup.style.display = 'block';
        }
    });

    // Generate AI track based on preferences - completely revamped for reliability and format compatibility
    async function generateAiTrack(mood, style, duration) {
        try {
            // Ask for user permission to play audio
            const userPermission = await requestAudioPermission();
            if (!userPermission) {
                showNotification('Audio permission is required to play music', 'warning');
                return;
            }

            // Create track key from mood and style
            const trackKey = `${mood}_${style}`;

            // Get track URLs for different formats - now using free sources
            let trackFormats = getFreeAudioSources(mood, style);

        // Verify the track formats are valid
        if (!trackFormats) {
            console.error('Invalid track formats for', trackKey);
            // Fallback to a known working track
            trackFormats = {
                mp3: 'https://cdn.pixabay.com/download/audio/2022/03/15/audio_c8e0386e38.mp3?filename=ambient-piano-amp-strings-10711.mp3',
                fallback: 'https://cdn.pixabay.com/download/audio/2022/03/15/audio_c8e0386e38.mp3?filename=ambient-piano-amp-strings-10711.mp3'
            };
        }

        // Set duration based on selection
        let durationMinutes;
        switch(duration) {
            case 'short':
                durationMinutes = '2-3';
                aiTotalDuration = 150; // 2.5 minutes in seconds
                break;
            case 'long':
                durationMinutes = '10-15';
                aiTotalDuration = 750; // 12.5 minutes in seconds
                break;
            case 'medium':
            default:
                durationMinutes = '5-7';
                aiTotalDuration = 360; // 6 minutes in seconds
        }

        // Generate track name and description
        const trackName = generateTrackName(mood, style);
        const trackDescription = generateTrackDescription(mood, style, durationMinutes);

        // Update UI
        aiTrackName.textContent = trackName;
        aiTrackDescription.textContent = trackDescription;

        // Stop any currently playing audio
        if (aiAudioElement && !aiAudioElement.paused) {
            aiAudioElement.pause();
        }

        // Create or recreate audio element to avoid issues
        try {
            // Always create a fresh audio element to avoid stale state issues
            if (aiAudioElement) {
                // Remove old event listeners to prevent memory leaks
                aiAudioElement.onended = null;
                aiAudioElement.onerror = null;
                aiAudioElement.onloadeddata = null;
            }

            // Log supported audio formats for debugging
            if (window.AudioUtils) {
                const supportedFormats = window.AudioUtils.logSupportedFormats();
                console.log('Browser supports these audio formats:', supportedFormats);
            }

            // Determine the best audio format to use
            let selectedUrl = '';

            // Check if AudioUtils is available
            if (window.AudioUtils) {
                // Try to find the best format using our utility
                const formats = window.AudioUtils.getSupportedFormats();

                if (formats.mp3 && trackFormats.mp3) {
                    selectedUrl = trackFormats.mp3;
                    console.log('Using MP3 format');
                } else if (formats.ogg && trackFormats.ogg) {
                    selectedUrl = trackFormats.ogg;
                    console.log('Using OGG format');
                } else if (formats.wav && trackFormats.wav) {
                    selectedUrl = trackFormats.wav;
                    console.log('Using WAV format');
                } else if (trackFormats.fallback) {
                    selectedUrl = trackFormats.fallback;
                    console.log('Using fallback format');
                } else {
                    // Last resort fallback
                    selectedUrl = 'https://cdn.pixabay.com/download/audio/2022/03/15/audio_c8e0386e38.mp3?filename=ambient-piano-amp-strings-10711.mp3';
                    console.log('Using emergency fallback URL');
                }
            } else {
                // If AudioUtils is not available, try MP3 first, then fallback
                selectedUrl = trackFormats.mp3 || trackFormats.fallback || 'https://cdn.pixabay.com/download/audio/2022/03/15/audio_c8e0386e38.mp3?filename=ambient-piano-amp-strings-10711.mp3';
            }

            console.log('Selected audio URL:', selectedUrl);

            // Signal that we're starting to load audio
            audioOperationQueue.startLoading();

            // Create new audio element with enhanced browser compatibility
            if (window.AudioUtils) {
                console.log('Using enhanced AudioUtils to create audio element');

                // Use the createReliableAudio method for maximum compatibility
                aiAudioElement = window.AudioUtils.createReliableAudio({
                    // Provide multiple source formats for the browser to choose from
                    sources: [
                        // Primary format based on browser detection
                        { src: selectedUrl, type: window.AudioUtils.getTypeFromFormat(selectedUrl.split('.').pop().toLowerCase()) },
                        // Add additional formats if available
                        trackFormats.mp3 ? { src: trackFormats.mp3, type: 'audio/mpeg' } : null,
                        trackFormats.m4a ? { src: trackFormats.m4a, type: 'audio/mp4; codecs="mp4a.40.2"' } : null,
                        trackFormats.ogg ? { src: trackFormats.ogg, type: 'audio/ogg; codecs="vorbis"' } : null,
                        trackFormats.wav ? { src: trackFormats.wav, type: 'audio/wav; codecs="1"' } : null,
                        trackFormats.aac ? { src: trackFormats.aac, type: 'audio/aac' } : null,
                        trackFormats.webm ? { src: trackFormats.webm, type: 'audio/webm; codecs="vorbis"' } : null
                    ].filter(Boolean), // Remove null entries

                    // Provide a reliable fallback that works in all browsers
                    fallbackUrl: trackFormats.fallback || 'https://cdn.pixabay.com/download/audio/2022/03/15/audio_c8e0386e38.mp3?filename=ambient-piano-amp-strings-10711.mp3',

                    // Set audio properties
                    volume: aiVolume ? aiVolume.value / 100 : lastVolume,
                    loop: true,
                    preload: 'auto',

                    // Success callback
                    onSuccess: (audioElement) => {
                        console.log('Audio loaded successfully via createReliableAudio');
                        playAiMusicBtn.disabled = false;
                        // Signal that loading is complete
                        audioOperationQueue.completeLoading();
                    },

                    // Error callback
                    onError: (error) => {
                        console.error('Audio error in createReliableAudio:', error);
                        const errorMessage = error.target && error.target.error ?
                            error.target.error.message : 'Unknown error';

                        console.error(`Audio error details: ${errorMessage}`);

                        // Signal that loading is complete even if there was an error
                        audioOperationQueue.completeLoading();

                        // Alert is handled by the createReliableAudio method
                        // which will automatically try the fallback URL
                    }
                });

                // Set additional properties
                aiAudioElement.crossOrigin = 'anonymous';

            } else {
                // Fallback for browsers without AudioUtils
                console.log('AudioUtils not available, using basic Audio constructor');

                // Create a new audio element with multiple source elements for better compatibility
                aiAudioElement = document.createElement('audio');
                aiAudioElement.loop = true;
                aiAudioElement.preload = 'auto';
                aiAudioElement.crossOrigin = 'anonymous';

                // Set volume
                const volumeValue = aiVolume ? aiVolume.value / 100 : lastVolume;
                aiAudioElement.volume = volumeValue;

                // Try to add multiple sources in order of likely compatibility
                const formats = [
                    { ext: 'mp3', type: 'audio/mpeg' },
                    { ext: 'm4a', type: 'audio/mp4; codecs="mp4a.40.2"' },
                    { ext: 'aac', type: 'audio/aac' },
                    { ext: 'ogg', type: 'audio/ogg; codecs="vorbis"' },
                    { ext: 'wav', type: 'audio/wav; codecs="1"' },
                    { ext: 'webm', type: 'audio/webm; codecs="vorbis"' }
                ];

                // Add source elements for each available format
                let sourcesAdded = false;
                formats.forEach(format => {
                    if (trackFormats[format.ext]) {
                        const source = document.createElement('source');
                        source.src = trackFormats[format.ext];
                        source.type = format.type;
                        aiAudioElement.appendChild(source);
                        sourcesAdded = true;
                    }
                });

                // If no sources were added, use the selected URL
                if (!sourcesAdded) {
                    aiAudioElement.src = selectedUrl;
                }

                // Add fallback for last resort
                if (trackFormats.fallback) {
                    const fallbackSource = document.createElement('source');
                    fallbackSource.src = trackFormats.fallback;
                    aiAudioElement.appendChild(fallbackSource);
                }

                // Add comprehensive event listeners for debugging
                aiAudioElement.addEventListener('error', (e) => {
                    console.error('Audio error:', e);
                    const errorMessage = e.target.error ? e.target.error.message : 'Unknown error';
                    console.error(`Audio error details: ${errorMessage}`);

                    // Try fallback URL if the selected one fails
                    if (trackFormats.fallback) {
                        console.log('Trying fallback URL:', trackFormats.fallback);
                        aiAudioElement.src = trackFormats.fallback;
                        aiAudioElement.load();
                    } else {
                        // Use a reliable CDN source as last resort
                        const emergencyFallback = 'https://cdn.pixabay.com/download/audio/2022/03/15/audio_c8e0386e38.mp3?filename=ambient-piano-amp-strings-10711.mp3';
                        console.log('Trying emergency fallback URL:', emergencyFallback);
                        aiAudioElement.src = emergencyFallback;
                        aiAudioElement.load();
                    }

                    // Signal that loading is complete even if there was an error
                    audioOperationQueue.completeLoading();
                });
            }

            // Add common event listeners regardless of creation method
            aiAudioElement.addEventListener('loadeddata', () => {
                console.log('Audio loaded successfully');
                // Enable play button once audio is loaded
                playAiMusicBtn.disabled = false;

                // Signal that loading is complete if not already done
                if (audioOperationQueue.isLoading) {
                    audioOperationQueue.completeLoading();
                }
            });

            aiAudioElement.addEventListener('canplaythrough', () => {
                console.log('Audio can play through without buffering');

                // Signal that loading is complete if not already done
                if (audioOperationQueue.isLoading) {
                    audioOperationQueue.completeLoading();
                }
            });

            // Set a timeout to ensure loading state is eventually cleared
            setTimeout(() => {
                if (audioOperationQueue.isLoading) {
                    console.warn('Audio loading taking too long, forcing completion');
                    audioOperationQueue.completeLoading();
                }
            }, 8000);

            // Load the audio if not already loading
            if (aiAudioElement.readyState === 0) {
                aiAudioElement.load();
            }

            // Log the audio element state
            console.log('Audio element created:', {
                src: aiAudioElement.src,
                volume: aiAudioElement.volume,
                muted: aiAudioElement.muted,
                loop: aiAudioElement.loop,
                preload: aiAudioElement.preload
            });

        } catch (error) {
            console.error('Error creating audio element:', error);
            alert('There was an error setting up the audio player. Please try again.');
        }

        // Reset progress
        aiCurrentTime = 0;
        updateAiProgress();
        } catch (error) {
            console.error('Error in generateAiTrack:', error);
            showNotification('Failed to generate music. Please try again.', 'error');

            // Hide generating animation, show player
            aiGenerating.classList.add('hidden');
            aiTrackPlayer.classList.remove('hidden');
        }
    }

    // Request audio permission from user - simplified version
    async function requestAudioPermission() {
        // Check if we've already asked for permission
        const hasPermission = localStorage.getItem('audioPermissionGranted');
        if (hasPermission === 'true') {
            return true;
        }

        // Simple confirm dialog for now to avoid modal issues
        const userConsent = confirm('🎵 This app would like to play relaxing music to help you relax. Allow audio playback?');

        if (userConsent) {
            localStorage.setItem('audioPermissionGranted', 'true');
            return true;
        } else {
            return false;
        }
    }

    // Get free audio sources based on mood and style
    function getFreeAudioSources(mood, style) {
        const freeAudioSources = {
            calm: {
                mp3: 'https://cdn.pixabay.com/download/audio/2022/03/15/audio_c8e0386e38.mp3?filename=ambient-piano-amp-strings-10711.mp3',
                fallback: 'https://cdn.pixabay.com/download/audio/2021/08/04/audio_12b0c7443c.mp3?filename=meditation-bell-6917.mp3'
            },
            energetic: {
                mp3: 'https://cdn.pixabay.com/download/audio/2022/08/02/audio_884fe92c21.mp3?filename=lofi-chill-medium-version-159456.mp3',
                fallback: 'https://cdn.pixabay.com/download/audio/2022/05/27/audio_1808fbf07a.mp3?filename=lofi-study-112191.mp3'
            },
            focused: {
                mp3: 'https://cdn.pixabay.com/download/audio/2022/05/27/audio_1808fbf07a.mp3?filename=lofi-study-112191.mp3',
                fallback: 'https://cdn.pixabay.com/download/audio/2022/08/02/audio_884fe92c21.mp3?filename=lofi-chill-medium-version-159456.mp3'
            },
            happy: {
                mp3: 'https://cdn.pixabay.com/download/audio/2022/03/15/audio_c8e0386e38.mp3?filename=ambient-piano-amp-strings-10711.mp3',
                fallback: 'https://cdn.pixabay.com/download/audio/2022/08/02/audio_884fe92c21.mp3?filename=lofi-chill-medium-version-159456.mp3'
            }
        };

        return freeAudioSources[mood] || freeAudioSources.calm;
    }

    // Audio operation queue to prevent interruptions
    const audioOperationQueue = {
        isLoading: false,
        isPlaying: false,
        pendingPlay: false,
        loadCompleteCallbacks: [],

        // Start loading audio
        startLoading: function() {
            this.isLoading = true;
            this.pendingPlay = false;
        },

        // Complete loading and execute callbacks
        completeLoading: function() {
            this.isLoading = false;
            // Execute all callbacks
            while (this.loadCompleteCallbacks.length > 0) {
                const callback = this.loadCompleteCallbacks.shift();
                try {
                    callback();
                } catch (e) {
                    console.error("Error in load complete callback:", e);
                }
            }

            // If there's a pending play request, execute it
            if (this.pendingPlay) {
                this.pendingPlay = false;
                playAiMusicInternal();
            }
        },

        // Queue a callback to run after loading completes
        onLoadComplete: function(callback) {
            if (!this.isLoading) {
                // If not loading, execute immediately
                callback();
            } else {
                // Otherwise queue for later
                this.loadCompleteCallbacks.push(callback);
            }
        },

        // Request to play audio
        requestPlay: function() {
            if (this.isLoading) {
                // If still loading, mark as pending play
                this.pendingPlay = true;
                return false;
            }
            return true;
        },

        // Start playing
        startPlaying: function() {
            this.isPlaying = true;
        },

        // Stop playing
        stopPlaying: function() {
            this.isPlaying = false;
            this.pendingPlay = false;
        }
    };

    // Play AI music - improved with better error handling and operation queuing
    function playAiMusic() {
        // If already playing, stop the music
        if (isAiPlaying) {
            stopAiMusic();
            return;
        }

        // Check if audio element exists
        if (!aiAudioElement) {
            console.error("No audio element available");
            alert("Audio player not ready. Please generate a track first.");
            return;
        }

        // Disable play button during attempt to prevent multiple clicks
        playAiMusicBtn.disabled = true;

        // If audio is still loading, queue the play request for when loading completes
        if (!audioOperationQueue.requestPlay()) {
            console.log("Audio still loading, queuing play request for when loading completes");
            playAiMusicBtn.disabled = false;
            return;
        }

        // Internal play function that actually plays the audio
        playAiMusicInternal();
    }

    // Internal function to handle the actual play operation
    function playAiMusicInternal() {
        // Make sure we have an audio element
        if (!aiAudioElement) return;

        // Disable play button during attempt
        playAiMusicBtn.disabled = true;

        // Make sure volume is set correctly before playing
        const volumeValue = aiVolume ? aiVolume.value / 100 : lastVolume;
        aiAudioElement.volume = volumeValue;
        // Update our stored volume
        lastVolume = volumeValue;
        console.log(`Setting volume before play: ${volumeValue * 100}%`);

        // Log audio element state before playing
        console.log('Audio element before play:', {
            src: aiAudioElement.src,
            paused: aiAudioElement.paused,
            volume: aiAudioElement.volume,
            muted: aiAudioElement.muted,
            readyState: aiAudioElement.readyState,
            networkState: aiAudioElement.networkState
        });

        // If audio is not ready yet, load it first
        if (aiAudioElement.readyState < 2) {
            console.log("Audio not ready, loading first");

            // Mark as loading
            audioOperationQueue.startLoading();

            // Set up event listeners for loading
            const loadedHandler = () => {
                console.log("Audio loaded successfully");
                aiAudioElement.removeEventListener('canplaythrough', loadedHandler);
                audioOperationQueue.completeLoading();
            };

            const errorHandler = (e) => {
                console.error("Error loading audio:", e);
                aiAudioElement.removeEventListener('error', errorHandler);
                audioOperationQueue.completeLoading();
                playAiMusicBtn.disabled = false;
            };

            // Add temporary event listeners
            aiAudioElement.addEventListener('canplaythrough', loadedHandler, { once: true });
            aiAudioElement.addEventListener('error', errorHandler, { once: true });

            // Start loading
            try {
                aiAudioElement.load();
            } catch (e) {
                console.error("Error calling load():", e);
                audioOperationQueue.completeLoading();
                playAiMusicBtn.disabled = false;
                return;
            }

            // Set a timeout in case loading takes too long
            setTimeout(() => {
                if (audioOperationQueue.isLoading) {
                    console.warn("Loading taking too long, proceeding anyway");
                    aiAudioElement.removeEventListener('canplaythrough', loadedHandler);
                    aiAudioElement.removeEventListener('error', errorHandler);
                    audioOperationQueue.completeLoading();
                }
            }, 5000);

            return;
        }

        // Mark as playing to prevent interruptions
        audioOperationQueue.startPlaying();

        // Add error handling for audio playback with timeout
        let playAttemptTimeout;
        let playPromise;

        try {
            // Use a try-catch to handle any synchronous errors
            playPromise = aiAudioElement.play();

            // Set a timeout to handle stalled play attempts
            playAttemptTimeout = setTimeout(() => {
                console.warn("Play attempt taking too long, might be stalled");
                playAiMusicBtn.disabled = false;
                audioOperationQueue.stopPlaying();
            }, 3000);
        } catch (e) {
            console.error("Synchronous error in play():", e);
            playAiMusicBtn.disabled = false;
            audioOperationQueue.stopPlaying();
            return;
        }

        if (playPromise !== undefined) {
            playPromise.then(() => {
                // Clear the timeout since play succeeded
                if (playAttemptTimeout) {
                    clearTimeout(playAttemptTimeout);
                    playAttemptTimeout = null;
                }

                // Playback started successfully
                console.log("Audio playback started successfully");
                playAiMusicBtn.textContent = 'Stop';
                playAiMusicBtn.classList.add('playing');
                playAiMusicBtn.disabled = false;
                isAiPlaying = true;

                // Check if we're playing a YouTube track
                const isYouTubeTrack = aiMusicSource.value === 'youtube';

                // Clear any existing interval
                if (aiProgressInterval) {
                    clearInterval(aiProgressInterval);
                }

                if (isYouTubeTrack) {
                    // For YouTube tracks, use the actual currentTime from the audio element
                    aiProgressInterval = setInterval(() => {
                        if (aiAudioElement) {
                            aiCurrentTime = Math.floor(aiAudioElement.currentTime);
                            aiTotalDuration = Math.floor(aiAudioElement.duration) || aiTotalDuration;
                            updateAiProgress();
                        }
                    }, 500); // Update more frequently for smoother progress
                } else {
                    // For AI-generated tracks, use the simulated timer
                    aiProgressInterval = setInterval(() => {
                        aiCurrentTime += 0.5; // Increment by half a second for smoother progress
                        if (aiCurrentTime > aiTotalDuration) {
                            aiCurrentTime = 0;
                        }
                        updateAiProgress();
                    }, 500);
                }
            }).catch(error => {
                // Clear the timeout since we got a response (even if it's an error)
                if (playAttemptTimeout) {
                    clearTimeout(playAttemptTimeout);
                    playAttemptTimeout = null;
                }

                // Stop the playing state
                audioOperationQueue.stopPlaying();

                // Auto-play was prevented or there was another error
                console.error("Audio playback failed:", error);

                // Special handling for interrupted play requests
                if (error.name === "AbortError" || error.message.includes("interrupted")) {
                    console.log("Play request was interrupted, will retry automatically");
                    // Don't show an error message, just retry after a short delay
                    setTimeout(() => {
                        if (!isAiPlaying) {
                            playAiMusic();
                        }
                    }, 500);
                    return;
                }

                // More detailed error handling for other errors
                let errorMessage = "Audio playback failed. ";

                if (error.name === "NotAllowedError") {
                    errorMessage += "This might be due to browser autoplay restrictions. Please try clicking play again.";
                } else if (error.name === "NotSupportedError") {
                    errorMessage += "The audio format is not supported by your browser.";
                } else {
                    errorMessage += error.message || "Please try a different track or refresh the page.";
                }

                console.error(errorMessage);
                alert(errorMessage);

                // Reset UI
                playAiMusicBtn.textContent = 'Play';
                playAiMusicBtn.classList.remove('playing');
                playAiMusicBtn.disabled = false;
                isAiPlaying = false;

                // Try to recreate the audio element if there was a fatal error
                if (error.name === "MediaError" || error.name === "NotSupportedError") {
                    console.log("Attempting to recreate audio element after error");
                    setTimeout(() => {
                        // Recreate with the same source
                        const currentSrc = aiAudioElement.src;
                        aiAudioElement = new Audio(currentSrc);
                        aiAudioElement.volume = volumeValue;
                    }, 1000);
                }
            });
        } else {
            // Clear the timeout if play() didn't return a promise
            if (playAttemptTimeout) {
                clearTimeout(playAttemptTimeout);
                playAttemptTimeout = null;
            }

            // Stop the playing state
            audioOperationQueue.stopPlaying();

            console.warn("Play didn't return a promise - older browser?");
            playAiMusicBtn.disabled = false;
        }
    }

    // Stop AI music - improved with better cleanup and queue management
    function stopAiMusic() {
        if (!aiAudioElement) return;

        console.log('Stopping audio playback');

        // Clear any pending play requests
        audioOperationQueue.pendingPlay = false;

        try {
            // Pause the audio
            aiAudioElement.pause();

            // Reset current time to beginning
            aiAudioElement.currentTime = 0;

            // Update UI
            playAiMusicBtn.textContent = 'Play';
            playAiMusicBtn.classList.remove('playing');
            playAiMusicBtn.disabled = false;
            isAiPlaying = false;

            // Update queue state
            audioOperationQueue.stopPlaying();

            // Stop progress tracking
            if (aiProgressInterval) {
                clearInterval(aiProgressInterval);
                aiProgressInterval = null;
            }

            // Reset progress display
            aiCurrentTime = 0;
            updateAiProgress();

            console.log('Audio playback stopped successfully');
        } catch (error) {
            console.error('Error stopping audio:', error);

            // Make sure queue state is reset even if there's an error
            audioOperationQueue.stopPlaying();
        }
    }

    // Update AI music progress display
    function updateAiProgress() {
        // Calculate percentage
        const percentage = (aiCurrentTime / aiTotalDuration) * 100;
        aiMusicProgressFill.style.width = `${percentage}%`;

        // Format time display
        const minutes = Math.floor(aiCurrentTime / 60);
        const seconds = aiCurrentTime % 60;
        aiMusicTime.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

        // Also display total duration for YouTube tracks
        if (aiMusicSource.value === 'youtube' && aiAudioElement && !isNaN(aiTotalDuration)) {
            const totalMinutes = Math.floor(aiTotalDuration / 60);
            const totalSeconds = aiTotalDuration % 60;
            const totalTime = `${totalMinutes.toString().padStart(2, '0')}:${totalSeconds.toString().padStart(2, '0')}`;
            aiMusicTime.textContent = `${aiMusicTime.textContent} / ${totalTime}`;
        }
    }

    // Generate a track name based on mood and style
    function generateTrackName(mood, style) {
        const moodNames = {
            calm: ['Tranquil', 'Serene', 'Peaceful', 'Gentle'],
            focus: ['Clarity', 'Concentration', 'Flow State', 'Deep Focus'],
            uplifting: ['Radiance', 'Elevation', 'Inspiration', 'Positivity'],
            sleep: ['Dreamscape', 'Night Journey', 'Slumber', 'Twilight']
        };

        const styleNames = {
            ambient: ['Atmosphere', 'Horizon', 'Spaces', 'Echoes'],
            lofi: ['Beats', 'Waves', 'Rhythms', 'Melodies'],
            classical: ['Sonata', 'Harmony', 'Symphony', 'Composition'],
            nature: ['Forest', 'Ocean', 'Mountain', 'Meadow']
        };

        // Get random name components
        const moodName = moodNames[mood][Math.floor(Math.random() * moodNames[mood].length)];
        const styleName = styleNames[style][Math.floor(Math.random() * styleNames[style].length)];

        return `${moodName} ${styleName}`;
    }

    // Generate a track description based on preferences
    function generateTrackDescription(mood, style, duration) {
        const moodDesc = {
            calm: 'calming and relaxing',
            focus: 'enhancing concentration and productivity',
            uplifting: 'elevating mood and energy',
            sleep: 'promoting deep relaxation and sleep'
        };

        const styleDesc = {
            ambient: 'ambient soundscapes',
            lofi: 'lo-fi beats',
            classical: 'classical-inspired melodies',
            nature: 'nature-infused sounds'
        };

        return `${duration} minute composition of ${styleDesc[style]} designed for ${moodDesc[mood]}.`;
    }

    // Generate YouTube track - completely revamped for reliability and format compatibility
    function generateYouTubeTrack(videoInfo, mood, style) {
        try {
            // Find suitable audio formats in order of preference
            const audioFormats = [];

            // First try to find audio/mp4 format
            const mp4Format = videoInfo.formats.find(format =>
                format.mimeType && format.mimeType.includes('audio/mp4')
            );

            if (mp4Format) {
                audioFormats.push(mp4Format);
            }

            // Then try to find audio/webm format
            const webmFormat = videoInfo.formats.find(format =>
                format.mimeType && format.mimeType.includes('audio/webm')
            );

            if (webmFormat) {
                audioFormats.push(webmFormat);
            }

            // Add any other audio formats
            const otherAudioFormats = videoInfo.formats.filter(format =>
                format.mimeType && format.mimeType.includes('audio/') &&
                format !== mp4Format && format !== webmFormat
            );

            audioFormats.push(...otherAudioFormats);

            // If no audio-only formats, try formats with both audio and video
            if (audioFormats.length === 0) {
                const audioVideoFormats = videoInfo.formats.filter(format =>
                    format.mimeType && format.hasAudio
                );

                audioFormats.push(...audioVideoFormats);
            }

            // Fallback to first format if nothing else found
            if (audioFormats.length === 0 && videoInfo.formats.length > 0) {
                audioFormats.push(videoInfo.formats[0]);
            }

            if (audioFormats.length === 0) {
                console.error('No suitable audio format found');
                alert('No suitable audio format found for this YouTube video. Please try another search.');

                // Fall back to a predefined track
                generateAiTrack('calm', 'ambient', 'medium');
                return;
            }

            // Log the available formats for debugging
            console.log('Available YouTube audio formats:', audioFormats.map(f => ({
                mimeType: f.mimeType,
                quality: f.quality,
                audioBitrate: f.audioBitrate
            })));

            // Select the best format
            const selectedFormat = audioFormats[0];
            console.log('Selected YouTube audio format:', {
                mimeType: selectedFormat.mimeType,
                quality: selectedFormat.quality,
                audioBitrate: selectedFormat.audioBitrate
            });

            // Set track name and description
            aiTrackName.textContent = videoInfo.title || 'YouTube Track';
            aiTrackDescription.textContent = `From YouTube: ${videoInfo.author || 'Unknown Artist'}`;

            // Set duration
            aiTotalDuration = parseInt(videoInfo.lengthSeconds) || 300;

            // Stop any currently playing audio
            if (aiAudioElement && !aiAudioElement.paused) {
                aiAudioElement.pause();
            }

            // Create or recreate audio element to avoid issues
            try {
                // Always create a fresh audio element to avoid stale state issues
                if (aiAudioElement) {
                    // Remove old event listeners to prevent memory leaks
                    aiAudioElement.onended = null;
                    aiAudioElement.onerror = null;
                    aiAudioElement.onloadeddata = null;
                }

                // Signal that we're starting to load audio
                audioOperationQueue.startLoading();

                // Get the video ID from the video info
                const videoId = videoInfo.id || videoInfo.videoId;

                // Create a list of alternative formats to try
                const alternativeUrls = [];

                // First try the direct streaming endpoint (most reliable)
                if (videoId) {
                    // Try Python-based streaming first (most reliable)
                    alternativeUrls.push({
                        src: `/api/youtube/stream/${videoId}?format=mp4&quality=highestaudio&usePython=true`,
                        type: 'audio/mp4'
                    });

                    // Try both MP4 and WebM formats with Node.js fallback
                    alternativeUrls.push({
                        src: `/api/youtube/stream/${videoId}?format=mp4&quality=highestaudio`,
                        type: 'audio/mp4'
                    });

                    alternativeUrls.push({
                        src: `/api/youtube/stream/${videoId}?format=webm&quality=highestaudio`,
                        type: 'audio/webm'
                    });

                    // Also add a direct audio proxy with just the video ID
                    alternativeUrls.push({
                        src: `/api/youtube/audio-proxy?videoId=${videoId}`,
                        type: 'audio/mp4'
                    });
                }

                // Then try the proxy endpoint with specific formats
                if (selectedFormat && selectedFormat.url) {
                    const proxyUrl = `/api/youtube/audio-proxy?url=${encodeURIComponent(selectedFormat.url)}`;
                    console.log('Using proxy URL:', proxyUrl);

                    alternativeUrls.push({
                        src: proxyUrl,
                        type: selectedFormat.mimeType || 'audio/mp4'
                    });
                }

                // Add other formats as alternatives
                audioFormats.slice(1, 3).forEach(format => {
                    if (format && format.url) {
                        const altProxyUrl = `/api/youtube/audio-proxy?url=${encodeURIComponent(format.url)}`;
                        alternativeUrls.push({
                            src: altProxyUrl,
                            type: format.mimeType || 'audio/mp4'
                        });
                    }
                });

                // Add a reliable fallback
                alternativeUrls.push({
                    src: 'https://cdn.pixabay.com/download/audio/2022/03/15/audio_c8e0386e38.mp3?filename=ambient-piano-amp-strings-10711.mp3',
                    type: 'audio/mpeg'
                });

                // Use AudioUtils if available for maximum compatibility
                if (window.AudioUtils) {
                    console.log('Using enhanced AudioUtils for YouTube audio');

                    // Use the createReliableAudio method for maximum compatibility
                    aiAudioElement = window.AudioUtils.createReliableAudio({
                        // Provide all alternative URLs
                        sources: alternativeUrls,

                        // Provide a reliable fallback
                        fallbackUrl: 'https://cdn.pixabay.com/download/audio/2022/03/15/audio_c8e0386e38.mp3?filename=ambient-piano-amp-strings-10711.mp3',

                        // Set audio properties
                        volume: aiVolume ? aiVolume.value / 100 : lastVolume,
                        loop: false,
                        preload: 'auto',

                        // Success callback
                        onSuccess: () => {
                            console.log('YouTube audio loaded successfully via createReliableAudio');
                            playAiMusicBtn.disabled = false;
                            // Signal that loading is complete
                            audioOperationQueue.completeLoading();
                        },

                        // Error callback
                        onError: (error) => {
                            console.error('YouTube audio error in createReliableAudio:', error);
                            const errorMessage = error.target && error.target.error ?
                                error.target.error.message : 'Unknown error';

                            console.error(`YouTube audio error details: ${errorMessage}`);

                            // Signal that loading is complete even if there was an error
                            audioOperationQueue.completeLoading();

                            // If all alternatives fail, fall back to a predefined track
                            setTimeout(() => {
                                alert('Could not play YouTube audio. Falling back to a predefined track.');
                                generateAiTrack('calm', 'ambient', 'medium');
                            }, 1000);
                        }
                    });

                    // Set additional properties
                    aiAudioElement.crossOrigin = 'anonymous';

                } else {
                    // Fallback for browsers without AudioUtils
                    console.log('AudioUtils not available, using basic Audio constructor for YouTube');

                    // Create a new audio element with multiple source elements
                    aiAudioElement = document.createElement('audio');
                    aiAudioElement.loop = false;
                    aiAudioElement.preload = 'auto';
                    aiAudioElement.crossOrigin = 'anonymous';

                    // Set volume
                    const volumeValue = aiVolume ? aiVolume.value / 100 : lastVolume;
                    aiAudioElement.volume = volumeValue;
                    console.log(`Setting initial YouTube volume: ${volumeValue * 100}%`);

                    // Add all alternative sources
                    alternativeUrls.forEach(source => {
                        const sourceElement = document.createElement('source');
                        sourceElement.src = source.src;
                        if (source.type) {
                            sourceElement.type = source.type;
                        }
                        aiAudioElement.appendChild(sourceElement);
                    });

                    // Add comprehensive event listeners for debugging
                    aiAudioElement.addEventListener('error', (e) => {
                        console.error('YouTube audio error:', e);
                        const errorMessage = e.target.error ? e.target.error.message : 'Unknown error';
                        console.error(`YouTube audio error details: ${errorMessage}`);

                        // Signal that loading is complete even if there was an error
                        audioOperationQueue.completeLoading();

                        // If all sources fail, fall back to a predefined track
                        if (e.target === aiAudioElement) {
                            alert(`Error loading YouTube audio: ${errorMessage}. Falling back to a predefined track.`);
                            generateAiTrack('calm', 'ambient', 'medium');
                        }
                    });

                    // Load the audio
                    aiAudioElement.load();
                }

                // Add common event listeners regardless of creation method
                aiAudioElement.addEventListener('loadeddata', () => {
                    console.log('YouTube audio loaded successfully');
                    // Enable play button once audio is loaded
                    playAiMusicBtn.disabled = false;

                    // Signal that loading is complete if not already done
                    if (audioOperationQueue.isLoading) {
                        audioOperationQueue.completeLoading();
                    }
                });

                aiAudioElement.addEventListener('canplaythrough', () => {
                    console.log('YouTube audio can play through without buffering');

                    // Signal that loading is complete if not already done
                    if (audioOperationQueue.isLoading) {
                        audioOperationQueue.completeLoading();
                    }
                });

                // Add event listener for when the track ends
                aiAudioElement.addEventListener('ended', () => {
                    console.log('YouTube track ended');
                    stopAiMusic();
                });

                // Add a stalled event handler for better recovery
                aiAudioElement.addEventListener('stalled', () => {
                    console.warn('YouTube audio playback stalled');
                    handleYouTubePlaybackError('Playback stalled');
                });

                // Add an error event handler with comprehensive recovery
                aiAudioElement.addEventListener('error', (e) => {
                    const errorMessage = e.target.error ? e.target.error.message : 'Unknown error';
                    console.error('YouTube audio error:', errorMessage);
                    handleYouTubePlaybackError(errorMessage);
                });

                // Set a timeout to ensure loading state is eventually cleared
                setTimeout(() => {
                    if (audioOperationQueue.isLoading) {
                        console.warn('YouTube audio loading taking too long, forcing completion');
                        audioOperationQueue.completeLoading();
                    }
                }, 8000);

                // Function to handle YouTube playback errors with recovery
                function handleYouTubePlaybackError(errorMessage) {
                    // If we're already in the process of handling an error, don't stack handlers
                    if (aiAudioElement.dataset.errorHandling === 'true') {
                        return;
                    }

                    // Mark that we're handling an error
                    aiAudioElement.dataset.errorHandling = 'true';

                    console.log('Attempting to recover from YouTube playback error');

                    // Try to reload the audio with the next source in alternativeUrls
                    const currentSrcIndex = alternativeUrls.findIndex(url =>
                        url.src === aiAudioElement.currentSrc ||
                        aiAudioElement.currentSrc.includes(url.src));

                    if (currentSrcIndex >= 0 && currentSrcIndex < alternativeUrls.length - 1) {
                        // Try the next source
                        const nextSource = alternativeUrls[currentSrcIndex + 1];
                        console.log('Trying next audio source:', nextSource.src);

                        aiAudioElement.src = nextSource.src;
                        aiAudioElement.load();

                        // Reset error handling flag after a delay
                        setTimeout(() => {
                            aiAudioElement.dataset.errorHandling = 'false';
                        }, 3000);
                    } else {
                        // We've tried all sources, fall back to a reliable CDN source
                        console.log('Tried all sources, falling back to reliable CDN source');

                        const fallbackUrl = 'https://cdn.pixabay.com/download/audio/2022/03/15/audio_c8e0386e38.mp3?filename=ambient-piano-amp-strings-10711.mp3';
                        aiAudioElement.src = fallbackUrl;
                        aiAudioElement.load();

                        // Update track info to reflect the fallback
                        aiTrackName.textContent = 'Relaxing Music (Fallback)';
                        aiTrackDescription.textContent = 'YouTube playback failed. Playing fallback relaxation track.';

                        // Reset error handling flag after a delay
                        setTimeout(() => {
                            aiAudioElement.dataset.errorHandling = 'false';
                        }, 3000);
                    }
                }

                // Log the audio element state
                console.log('YouTube audio element created:', {
                    src: aiAudioElement.src,
                    volume: aiAudioElement.volume,
                    muted: aiAudioElement.muted,
                    loop: aiAudioElement.loop,
                    preload: aiAudioElement.preload,
                    duration: aiTotalDuration
                });

            } catch (error) {
                console.error('Error creating YouTube audio element:', error);
                alert('There was an error setting up the YouTube audio player. Falling back to a predefined track.');
                generateAiTrack('calm', 'ambient', 'medium');
            }

            // Reset progress
            aiCurrentTime = 0;
            updateAiProgress();

        } catch (error) {
            console.error('Error in generateYouTubeTrack:', error);
            alert('Failed to process YouTube track. Falling back to a predefined track.');
            generateAiTrack('calm', 'ambient', 'medium');
        }
    }

    // Breathing exercise functionality
    let breathingInterval;
    let isBreathing = false;

    startBreathingBtn.addEventListener('click', () => {
        if (isBreathing) {
            clearInterval(breathingInterval);
            breathingCircle.classList.remove('inhale', 'exhale');
            breathingInstruction.textContent = 'Click to begin';
            startBreathingBtn.textContent = 'Start Exercise';
            isBreathing = false;
        } else {
            let phase = 'inhale';
            breathingCircle.classList.add('inhale');
            breathingInstruction.textContent = 'Breathe in...';
            startBreathingBtn.textContent = 'Stop Exercise';
            isBreathing = true;

            breathingInterval = setInterval(() => {
                if (phase === 'inhale') {
                    phase = 'hold';
                    breathingInstruction.textContent = 'Hold...';
                    setTimeout(() => {
                        if (isBreathing) {
                            phase = 'exhale';
                            breathingCircle.classList.remove('inhale');
                            breathingCircle.classList.add('exhale');
                            breathingInstruction.textContent = 'Breathe out...';
                        }
                    }, 2000);
                } else if (phase === 'exhale') {
                    phase = 'rest';
                    breathingInstruction.textContent = 'Rest...';
                    setTimeout(() => {
                        if (isBreathing) {
                            phase = 'inhale';
                            breathingCircle.classList.remove('exhale');
                            breathingCircle.classList.add('inhale');
                            breathingInstruction.textContent = 'Breathe in...';
                        }
                    }, 2000);
                }
            }, 4000);
        }
    });

    // Stretching exercises
    const stretches = [
        { name: 'Neck Rolls', instruction: 'Gently roll your head in a circular motion', duration: 30 },
        { name: 'Shoulder Rolls', instruction: 'Roll your shoulders forward, then backward', duration: 30 },
        { name: 'Wrist Stretches', instruction: 'Extend your arm and gently pull your fingers back', duration: 20 },
        { name: 'Side Stretch', instruction: 'Raise your arm over your head and lean to the opposite side', duration: 20 },
        { name: 'Ankle Circles', instruction: 'Lift one foot and rotate your ankle in both directions', duration: 20 }
    ];

    let currentStretchIndex = 0;
    let stretchTimer;
    let stretchCountdown;

    function updateStretchDisplay() {
        const stretch = stretches[currentStretchIndex];
        stretchInstruction.textContent = `${stretch.name}: ${stretch.instruction}`;
        stretchTime.textContent = formatTime(stretch.duration);
    }

    function formatTime(seconds) {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }

    updateStretchDisplay();

    prevStretchBtn.addEventListener('click', () => {
        if (stretchTimer) return; // Don't change during active stretch
        currentStretchIndex = (currentStretchIndex - 1 + stretches.length) % stretches.length;
        updateStretchDisplay();
    });

    nextStretchBtn.addEventListener('click', () => {
        if (stretchTimer) return; // Don't change during active stretch
        currentStretchIndex = (currentStretchIndex + 1) % stretches.length;
        updateStretchDisplay();
    });

    startStretchBtn.addEventListener('click', () => {
        if (stretchTimer) {
            // Stop the stretch
            clearInterval(stretchTimer);
            stretchTimer = null;
            stretchCountdown = null;
            updateStretchDisplay();
            startStretchBtn.textContent = 'Start';
            prevStretchBtn.disabled = false;
            nextStretchBtn.disabled = false;
        } else {
            // Start the stretch
            const stretch = stretches[currentStretchIndex];
            stretchCountdown = stretch.duration;
            stretchTime.textContent = formatTime(stretchCountdown);
            startStretchBtn.textContent = 'Stop';
            prevStretchBtn.disabled = true;
            nextStretchBtn.disabled = true;

            stretchTimer = setInterval(() => {
                stretchCountdown--;
                stretchTime.textContent = formatTime(stretchCountdown);

                if (stretchCountdown <= 0) {
                    clearInterval(stretchTimer);
                    stretchTimer = null;
                    startStretchBtn.textContent = 'Start';
                    prevStretchBtn.disabled = false;
                    nextStretchBtn.disabled = false;

                    // Auto-advance to next stretch
                    currentStretchIndex = (currentStretchIndex + 1) % stretches.length;
                    updateStretchDisplay();
                }
            }, 1000);
        }
    });

    // Mindfulness timer functionality
    let mindfulnessMinutes = 5;
    let mindfulnessSeconds = mindfulnessMinutes * 60;
    let mindfulnessInterval;

    timeButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            timeButtons.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
            mindfulnessMinutes = parseInt(btn.getAttribute('data-time'));
            mindfulnessSeconds = mindfulnessMinutes * 60;
            mindfulnessTimer.textContent = `${mindfulnessMinutes}:00`;
        });
    });

    startMindfulnessBtn.addEventListener('click', () => {
        if (mindfulnessInterval) {
            // Stop the timer
            clearInterval(mindfulnessInterval);
            mindfulnessInterval = null;
            mindfulnessSeconds = mindfulnessMinutes * 60;
            mindfulnessTimer.textContent = `${mindfulnessMinutes}:00`;
            startMindfulnessBtn.textContent = 'Start Mindfulness Session';
            timeButtons.forEach(btn => btn.disabled = false);
        } else {
            // Start the timer
            startMindfulnessBtn.textContent = 'Stop Session';
            timeButtons.forEach(btn => btn.disabled = true);

            mindfulnessInterval = setInterval(() => {
                mindfulnessSeconds--;
                const mins = Math.floor(mindfulnessSeconds / 60);
                const secs = mindfulnessSeconds % 60;
                mindfulnessTimer.textContent = `${mins}:${secs.toString().padStart(2, '0')}`;

                if (mindfulnessSeconds <= 0) {
                    clearInterval(mindfulnessInterval);
                    mindfulnessInterval = null;
                    startMindfulnessBtn.textContent = 'Start Mindfulness Session';
                    timeButtons.forEach(btn => btn.disabled = false);

                    // Play sound when timer ends
                    const audio = new Audio('https://soundbible.com/mp3/Zen_Temple_Bell-SoundBible.com-331362457.mp3');
                    audio.play();
                }
            }, 1000);
        }
    });

    // Profile modal functionality
    profileButton.addEventListener('click', () => {
        updateProfileUI();
        profileModal.classList.add('show');
    });

    // Close profile modal when clicking the X button
    if (closeModalBtn) {
        closeModalBtn.addEventListener('click', () => {
            profileModal.classList.remove('show');
        });
    } else {
        console.error('Close modal button not found for profile modal');
    }

    // Close modal when clicking outside
    window.addEventListener('click', (e) => {
        if (e.target === profileModal) {
            profileModal.classList.remove('show');
        }
    });

    saveProfileBtn.addEventListener('click', () => {
        // Save profile data
        userProfile.name = document.getElementById('profileName').value;
        userProfile.age = document.getElementById('profileAge').value;
        userProfile.grade = document.getElementById('profileGrade').value;
        userProfile.major = document.getElementById('profileMajor').value;

        // Get selected stressors
        userProfile.stressors = [];
        document.querySelectorAll('input[name="stressor"]:checked').forEach(checkbox => {
            userProfile.stressors.push(checkbox.value);
        });

        // Save to localStorage
        localStorage.setItem('userProfile', JSON.stringify(userProfile));

        // Update UI elements
        updateProfileUI();
        document.getElementById('profileAvatar').src = userProfile.avatarUrl;

        // Close modal
        profileModal.classList.remove('show');

        // Show feedback
        alert('Profile saved successfully!');
    });

    document.getElementById('uploadAvatarBtn').addEventListener('click', () => {
        document.getElementById('avatarFileInput').click();
    });

    document.getElementById('avatarFileInput').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (!file) return;

        if (!file.type.match('image.*')) {
            alert('Please select an image file.');
            return;
        }

        const reader = new FileReader();

        reader.onload = function(event) {
            // Update profile preview and user's avatar
            userProfile.avatarUrl = event.target.result;
            document.getElementById('profileAvatarPreview').src = event.target.result;
            document.getElementById('profileAvatar').src = event.target.result;
        }

        reader.readAsDataURL(file);
    });

    // Update slider display
    sleepInput.addEventListener('input', () => updateSliderDisplay(sleepInput, sleepValueDisplay, 'hours'));
    homeworkInput.addEventListener('input', () => updateSliderDisplay(homeworkInput, homeworkValueDisplay));
    socialInput.addEventListener('input', () => updateSliderDisplay(socialInput, socialValueDisplay));
    extracurricularsInput.addEventListener('input', () => updateSliderDisplay(extracurricularsInput, extracurricularsValueDisplay));
    nutritionInput.addEventListener('input', () => updateSliderDisplay(nutritionInput, nutritionValueDisplay));
    screenInput.addEventListener('input', () => updateSliderDisplay(screenInput, screenValueDisplay, 'hours'));

    function updateSliderDisplay(input, display, unit = '') {
        display.textContent = input.value + (unit ? ` ${unit}` : '');
    }

    // Initialize stress history data
    if (stressHistory.length > 0) {
        updateStressHistory();
    }

    // Save the current stress result
    saveResultButton.addEventListener('click', () => {
        const stressLevel = stressLevelText.textContent;
        const emoji = stressEmoji.textContent;
        const advice = stressAdviceText.textContent;
        const score = parseInt(stressMeterFill.style.width) || 50;

        // Create a new record
        const newRecord = {
            date: new Date().toISOString(),
            level: stressLevel,
            score: score,
            emoji: emoji,
            advice: advice
        };

        // Add to history
        stressHistory.push(newRecord);

        // Keep only the last 30 days
        if (stressHistory.length > 30) {
            stressHistory = stressHistory.slice(-30);
        }

        // Save to localStorage
        localStorage.setItem('stressHistory', JSON.stringify(stressHistory));

        // Update display
        updateStressHistory();

        // Feedback to user
        saveResultButton.textContent = 'Saved!';
        setTimeout(() => {
            saveResultButton.textContent = 'Save Today\'s Result';
        }, 2000);
    });

    // Clear history
    clearHistoryButton.addEventListener('click', () => {
        if (confirm('Are you sure you want to clear all your stress history?')) {
            stressHistory = [];
            localStorage.removeItem('stressHistory');
            historySection.style.display = 'none';
        }
    });

    // Update stress history
    function updateStressHistory() {
        // Update history list
        historyList.innerHTML = '';

        if (stressHistory.length === 0) {
            historyList.innerHTML = '<p>No stress records yet. Save your first result!</p>';
            return;
        }

        stressHistory.forEach((record, index) => {
            const recordDate = new Date(record.date);
            const formattedDate = recordDate.toLocaleDateString('en-US', {
                weekday: 'short',
                month: 'short',
                day: 'numeric'
            });

            const levelClass = `level-${record.level.toLowerCase().replace(' ', '-')}`;

            const historyItem = document.createElement('div');
            historyItem.className = 'history-item';
            historyItem.innerHTML = `
                <span class="history-date">${formattedDate}</span>
                <span class="history-level ${levelClass}">${record.level} ${record.emoji}</span>
            `;
            historyList.appendChild(historyItem);
        });

        // Update chart
        const chartData = prepareChartData();
        updateStressChart(chartData);

        // Show history section
        historySection.style.display = 'block';
    }

    // Prepare chart data
    function prepareChartData() {
        // Sort records by date
        const sortedHistory = [...stressHistory].sort((a, b) => new Date(a.date) - new Date(b.date));

        // Prepare data for chart
        const labels = sortedHistory.map(record => {
            const date = new Date(record.date);
            return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        });

        const data = sortedHistory.map(record => record.score);

        return { labels, data };
    }

    // Update stress chart
    function updateStressChart(chartData) {
        // Destroy previous chart if it exists
        if (stressChartInstance) {
            stressChartInstance.destroy();
        }

        // Create new chart
        const ctx = stressChart.getContext('2d');
        stressChartInstance = new Chart(ctx, {
            type: 'line',
            data: {
                labels: chartData.labels,
                datasets: [{
                    label: 'Stress Score',
                    data: chartData.data,
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    borderWidth: 2,
                    tension: 0.3,
                    fill: true,
                    pointBackgroundColor: '#3498db',
                    pointRadius: 4,
                    pointHoverRadius: 6
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        title: {
                            display: true,
                            text: 'Stress Score'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Date'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            afterLabel: function(context) {
                                const index = context.dataIndex;
                                const record = stressHistory.find(r => {
                                    const date = new Date(r.date);
                                    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }) === context.label;
                                });
                                if (record) {
                                    return [
                                        `Level: ${record.level} ${record.emoji}`,
                                        `Advice: ${record.advice}`
                                    ];
                                }
                                return '';
                            }
                        }
                    }
                }
            }
        });
    }

    predictButton.addEventListener('click', async () => {
        resultArea.style.display = 'block';
        predictionOutput.style.display = 'none';
        loadingIndicator.style.display = 'block';
        document.body.className = ''; // Reset background color

        const studentData = {
            sleep: parseFloat(sleepInput.value),
            homeworkLoad: parseInt(homeworkInput.value),
            upcomingExams: parseInt(examsInput.value),
            socialInteraction: parseFloat(socialInput.value),
            extracurriculars: parseFloat(extracurricularsInput.value),
            nutrition: parseFloat(nutritionInput.value),
            screenTime: parseFloat(screenInput.value)
        };

        const systemPrompt = `You are a helpful AI assistant that predicts a student's daily stress level based on several factors. You also provide a brief, positive piece of advice and personalized tips. Be encouraging, concise, and provide actionable advice. Use a friendly, supportive tone.
Respond ONLY with a JSON object matching this schema:
{
  "stressLevel": "string (Low, Moderate, High, or Very High)",
  "stressScore": "number between 0 and 100, with 0 being no stress and 100 being maximum stress",
  "advice": "string (1-2 concise sentences of positive advice)",
  "emoji": "string (a single emoji representing the stress level or mood)",
  "tips": ["array of 3 brief, specific tips based on their inputs"]
}
Do not include any other text or explanations outside of the JSON object. Keep advice encouraging and actionable if possible.`;

        const userPrompt = `Predict the stress level for a student with the following daily factors:
- Sleep: ${studentData.sleep} hours
- Homework Load (0-10 scale): ${studentData.homeworkLoad}
- Number of Upcoming Exams: ${studentData.upcomingExams}
- Social Interaction (0-5 scale): ${studentData.socialInteraction}
- Extracurricular Activities (0-5 scale): ${studentData.extracurriculars}
- Nutrition Quality (0-5 scale): ${studentData.nutrition}
- Screen Time: ${studentData.screenTime} hours

Consider these factors together to determine stress level. For example, low sleep, high homework load, and many exams would indicate high stress. Good social interaction and nutrition might lower stress levels.`;

        try {
            // Use our dual model system for faster stress prediction
            console.log('Making stress prediction request...');

            const response = await fetch('/api/chat/completions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    messages: [
                        {
                            role: "system",
                            content: systemPrompt
                        },
                        {
                            role: "user",
                            content: userPrompt
                        }
                    ],
                    modelPreference: 'auto' // Use best available model for fastest results
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const completion = await response.json();
            console.log('Stress prediction response received from:', completion.model_used || 'unknown model');

            // Parse the JSON response
            let result;
            try {
                // Handle potential HTML in the response by extracting just the JSON
                const jsonMatch = completion.content.match(/\{[\s\S]*\}/);
                const jsonString = jsonMatch ? jsonMatch[0] : completion.content;
                result = JSON.parse(jsonString);
            } catch (parseError) {
                console.error("Error parsing JSON response:", parseError);
                // Fallback to a simple object if parsing fails
                result = {
                    stressLevel: "Moderate",
                    stressScore: 50,
                    advice: "Take regular breaks and practice deep breathing.",
                    emoji: "😌",
                    tips: ["Take short breaks", "Stay hydrated", "Get enough sleep"]
                };
            }

            // Safely set text content to avoid HTML rendering issues
            stressLevelText.textContent = result.stressLevel || "N/A";
            stressEmoji.textContent = result.emoji || "🤔";
            stressAdviceText.textContent = result.advice || "No specific advice available.";

            // Update stress meter
            const stressScore = result.stressScore || 50;
            stressMeterFill.style.width = `${stressScore}%`;

            // Update personalized tips - using textContent to avoid HTML issues
            personalizedTips.innerHTML = '';
            if (result.tips && result.tips.length > 0) {
                const tipsHeading = document.createElement('h3');
                tipsHeading.textContent = 'Personalized Tips:';
                personalizedTips.appendChild(tipsHeading);

                const tipsList = document.createElement('ul');
                result.tips.forEach(tip => {
                    const tipItem = document.createElement('li');
                    tipItem.textContent = tip; // Use textContent to safely set content
                    tipsList.appendChild(tipItem);
                });
                personalizedTips.appendChild(tipsList);
            }

            // Update background based on stress level
            const stressClass = (result.stressLevel || "").toLowerCase().replace(' ', '-');
            if (['low', 'moderate', 'high', 'very-high'].includes(stressClass)) {
                 document.body.classList.add(`stress-${stressClass}`);
            }

            // Store the prediction for use in chat
            lastStressPrediction = {
                level: result.stressLevel || "Moderate",
                score: result.stressScore || 50,
                advice: result.advice || "",
                tips: result.tips || []
            };

            // Update websim with stress data if available
            if (typeof websim !== 'undefined' && typeof websim.updateStressData === 'function') {
                websim.updateStressData(lastStressPrediction, studentData);

                // Also dispatch an event for any listeners
                const event = new CustomEvent('stressDataUpdated', {
                    detail: {
                        prediction: lastStressPrediction,
                        studentData: studentData
                    }
                });
                document.dispatchEvent(event);
            }

        } catch (error) {
            console.error("Error fetching prediction:", error);
            stressLevelText.textContent = "Error";
            stressEmoji.textContent = "😟";
            stressAdviceText.textContent = "Could not retrieve prediction. Please try again.";
            document.body.className = ''; // Reset on error
        } finally {
            loadingIndicator.style.display = 'none';
            predictionOutput.style.display = 'block';
        }
    });

    // Call updateWelcomeMessage and updateGreetingText on page load
    updateWelcomeMessage();
    updateGreetingText();

    // Add direct event listener to profile modal close button
    const profileModalCloseBtn = document.querySelector('#profileModal .close-modal');
    if (profileModalCloseBtn) {
        profileModalCloseBtn.addEventListener('click', () => {
            profileModal.classList.remove('show');
        });
    }

    // Run audio format compatibility test
    if (window.AudioUtils) {
        testAudioCompatibility();
    }
});

// Test audio format compatibility and log results
function testAudioCompatibility() {
    console.log('Running audio format compatibility test...');

    // Log browser information
    console.log('Browser information:', window.AudioUtils.browserInfo);

    // Log supported formats
    const supportedFormats = window.AudioUtils.getSupportedFormats();
    console.log('Supported audio formats:', supportedFormats);

    // Log recommended formats
    const recommendedFormats = window.AudioUtils.getRecommendedFormats();
    console.log('Recommended formats for this browser:', recommendedFormats);

    // Test a few common audio formats
    const testFormats = [
        { url: 'https://cdn.pixabay.com/download/audio/2022/03/15/audio_c8e0386e38.mp3', format: 'mp3' },
        { url: 'https://soundbible.com/grab.php?id=1661&type=mp3', format: 'mp3' },
        { url: 'https://soundbible.com/grab.php?id=1661&type=ogg', format: 'ogg' },
        { url: 'https://soundbible.com/grab.php?id=1661&type=wav', format: 'wav' }
    ];

    testFormats.forEach(test => {
        const canPlay = window.AudioUtils.canPlayUrl(test.url);
        console.log(`Can play ${test.format} (${test.url}): ${canPlay}`);
    });

    // Create a test audio element with multiple sources
    const testAudio = document.createElement('audio');
    testAudio.preload = 'none'; // Don't actually load the audio

    // Add sources for each recommended format
    recommendedFormats.forEach(format => {
        const source = document.createElement('source');
        source.src = `https://example.com/test.${format}`; // Dummy URL
        source.type = window.AudioUtils.getTypeFromFormat(format);
        testAudio.appendChild(source);
    });

    // Log the test audio element
    console.log('Test audio element with recommended sources:', testAudio);
    console.log('Audio format compatibility test complete');
}