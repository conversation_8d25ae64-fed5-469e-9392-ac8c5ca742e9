{"name": "student-stress-predictor", "version": "1.0.0", "description": "Student Stress Predictor with Ollama integration", "main": "server.js", "scripts": {"start": "node server.js", "postinstall": "echo 'Please install Python dependencies: pip install yt-dlp pytube ffmpeg-python requests'"}, "dependencies": {"axios": "^1.6.2", "cors": "^2.8.5", "express": "^4.18.2", "youtube-search-api": "^1.2.2", "ytdl-core": "^4.11.5", "@distube/ytdl-core": "^4.13.5"}, "engines": {"node": ">=14.0.0"}, "engineStrict": true}